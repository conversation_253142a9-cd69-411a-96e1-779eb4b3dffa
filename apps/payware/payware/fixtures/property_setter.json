[{"default_value": null, "doc_type": "Employee", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "company", "modified": "2020-09-26 11:13:17.460034", "name": "Employee-company-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Leave Application", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "modified": "2020-12-25 19:43:54.458401", "name": "Leave Application-naming_series-options", "parent": null, "parentfield": null, "parenttype": null, "property": "options", "property_type": "Text", "row_name": null, "value": "HR-LAP-.YYYY.-"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "status", "modified": "2021-01-25 12:16:47.621855", "name": "Loan-status-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Payroll Entry", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "branch", "modified": "2021-01-25 17:32:07.540843", "name": "Payroll Entry-branch-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Payroll Entry", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "company", "modified": "2021-01-25 17:41:52.825218", "name": "Payroll Entry-company-print_width", "parent": null, "parentfield": null, "parenttype": null, "property": "print_width", "property_type": "Data", "row_name": null, "value": "4"}, {"default_value": null, "doc_type": "Additional Salary", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "modified": "2021-01-26 21:25:26.857150", "name": "Additional Salary-naming_series-options", "parent": null, "parentfield": null, "parenttype": null, "property": "options", "property_type": "Text", "row_name": null, "value": "HR-ADS-.YY.-.MM.-"}, {"default_value": null, "doc_type": "Payroll Entry", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "company", "modified": "2021-01-27 15:17:50.942956", "name": "Payroll Entry-company-in_standard_filter", "parent": null, "parentfield": null, "parenttype": null, "property": "in_standard_filter", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Additional Salary", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "overwrite_salary_structure_amount", "modified": "2021-02-04 17:43:37.669715", "name": "Additional Salary-overwrite_salary_structure_amount-default", "parent": null, "parentfield": null, "parenttype": null, "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Salary Structure Assignment", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "company", "modified": "2021-02-23 10:03:37.159838", "name": "Salary Structure Assignment-company-in_standard_filter", "parent": null, "parentfield": null, "parenttype": null, "property": "in_standard_filter", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Attendance", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "status", "modified": "2021-03-15 11:14:59.067804", "name": "Attendance-status-allow_on_submit", "parent": null, "parentfield": null, "parenttype": null, "property": "allow_on_submit", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Attendance", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "DocType", "field_name": null, "modified": "2021-03-15 11:18:32.644354", "name": "Attendance-main-track_changes", "parent": null, "parentfield": null, "parenttype": null, "property": "track_changes", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Attendance", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "leave_type", "modified": "2021-03-15 11:20:07.393225", "name": "Attendance-leave_type-allow_on_submit", "parent": null, "parentfield": null, "parenttype": null, "property": "allow_on_submit", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Employee", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "image", "modified": "2021-04-03 10:50:06.210253", "name": "Employee-image-hidden", "parent": null, "parentfield": null, "parenttype": null, "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Salary Structure Assignment", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base", "modified": "2018-12-02 20:39:05.938112", "name": "Salary Structure Assignment-base-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Salary Structure Assignment", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "employee", "modified": "2018-12-02 20:40:50.879610", "name": "Salary Structure Assignment-employee-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Payroll Entry", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "end_date", "modified": "2019-05-25 11:23:38.876258", "name": "Payroll Entry-end_date-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "repayment_method", "modified": "2019-06-29 12:24:57.717189", "name": "Loan-repayment_method-options", "parent": null, "parentfield": null, "parenttype": null, "property": "options", "property_type": "Text", "row_name": null, "value": "Repay Fixed Amount per Period\nRepay Over Number of Periods"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "applicant_name", "modified": "2019-06-29 18:38:45.429450", "name": "Loan-applicant_name-in_standard_filter", "parent": null, "parentfield": null, "parenttype": null, "property": "in_standard_filter", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "applicant_name", "modified": "2019-06-29 18:38:45.433671", "name": "Loan-applicant_name-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "loan_type", "modified": "2019-06-29 18:38:45.437356", "name": "Loan-loan_type-in_standard_filter", "parent": null, "parentfield": null, "parenttype": null, "property": "in_standard_filter", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "loan_type", "modified": "2019-06-29 18:38:45.440766", "name": "Loan-loan_type-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Payroll Entry", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "posting_date", "modified": "2019-07-03 11:53:16.555060", "name": "Payroll Entry-posting_date-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "loan_amount", "modified": "2020-01-13 00:18:39.422736", "name": "Loan-loan_amount-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "DocType", "field_name": null, "modified": "2020-01-13 00:19:47.007987", "name": "Loan-search_fields", "parent": null, "parentfield": null, "parenttype": null, "property": "search_fields", "property_type": "Data", "row_name": null, "value": "posting_date, applicant_name"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "status", "modified": "2020-01-13 00:20:22.485670", "name": "Loan-status-in_standard_filter", "parent": null, "parentfield": null, "parenttype": null, "property": "in_standard_filter", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "posting_date", "modified": "2020-01-13 00:20:44.941164", "name": "Loan-posting_date-in_list_view", "parent": null, "parentfield": null, "parenttype": null, "property": "in_list_view", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Employee", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "DocType", "field_name": null, "modified": "2021-05-05 16:09:32.360734", "name": "Employee-main-track_changes", "parent": null, "parentfield": null, "parenttype": null, "property": "track_changes", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Employee", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "job_profile", "modified": "2021-05-22 12:06:00.778373", "name": "Employee-job_profile-permlevel", "parent": null, "parentfield": null, "parenttype": null, "property": "permlevel", "property_type": "Int", "row_name": null, "value": "2"}, {"default_value": null, "doc_type": "Salary Structure", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "earnings", "modified": "2021-05-24 15:24:12.413854", "name": "Salary Structure-earnings-allow_on_submit", "parent": null, "parentfield": null, "parenttype": null, "property": "allow_on_submit", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Salary Structure", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "deductions", "modified": "2021-05-24 15:24:22.727699", "name": "Salary Structure-deductions-allow_on_submit", "parent": null, "parentfield": null, "parenttype": null, "property": "allow_on_submit", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Loan", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "repay_from_salary", "modified": "2021-05-28 13:54:33.247762", "name": "Loan-repay_from_salary-allow_on_submit", "parent": null, "parentfield": null, "parenttype": null, "property": "allow_on_submit", "property_type": "Check", "row_name": null, "value": "1"}]