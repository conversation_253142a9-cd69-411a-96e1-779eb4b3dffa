{"creation": "2020-01-12 15:40:12.210088", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["designation_kpi_template", "employee", "column_break_2", "designation", "employee_full_name", "section_break_2", "hr_perspective_1", "column_break_4", "perspective_weightage_1", "section_break_6", "kpi_details_1", "section_break_8", "hr_perspective_2", "column_break_10", "perspective_weightage_2", "section_break_12", "kpi_details_2", "section_break_14", "hr_perspective_3", "column_break_16", "perspective_weightage_3", "section_break_18", "kpi_details_3", "section_break_20", "hr_perspective_4", "column_break_22", "perspective_weightage_4", "section_break_24", "kpi_details_4", "section_break_26", "total_employee_rating", "column_break_9", "total_manager_rating"], "fields": [{"fieldname": "designation_kpi_template", "fieldtype": "Link", "label": "Designation KPI Template", "options": "Designation KPI Template", "reqd": 1}, {"fieldname": "employee", "fieldtype": "Link", "label": "Employee", "options": "Employee", "reqd": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fetch_from": "goal_sheet_template.designation", "fieldname": "designation", "fieldtype": "Link", "label": "Designation", "options": "Designation", "read_only": 1, "reqd": 1}, {"fetch_from": "employee.employee_name", "fieldname": "employee_full_name", "fieldtype": "Data", "label": "Employee Full Name", "read_only": 1}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "hr_perspective_1", "fieldtype": "Link", "in_list_view": 1, "label": "HR Perspective 1", "options": "HR Perspective", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fetch_from": "hr_perspective_1.weightage", "fieldname": "perspective_weightage_1", "fieldtype": "Percent", "in_list_view": 1, "label": "Perspective Weightage 1", "read_only": 1, "reqd": 1}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "kpi_details_1", "fieldtype": "Table", "label": "KPI Details 1", "options": "Goal Sheet Detail"}, {"fieldname": "section_break_8", "fieldtype": "Section Break"}, {"fieldname": "hr_perspective_2", "fieldtype": "Link", "in_list_view": 1, "label": "HR Perspective 2", "options": "HR Perspective", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fetch_from": "hr_perspective_2.weightage", "fieldname": "perspective_weightage_2", "fieldtype": "Percent", "in_list_view": 1, "label": "Perspective Weightage 2", "read_only": 1, "reqd": 1}, {"fieldname": "section_break_12", "fieldtype": "Section Break"}, {"fieldname": "kpi_details_2", "fieldtype": "Table", "label": "KPI Details 2", "options": "Goal Sheet Detail"}, {"fieldname": "section_break_14", "fieldtype": "Section Break"}, {"fieldname": "hr_perspective_3", "fieldtype": "Link", "label": "HR Perspective 3", "options": "HR Perspective", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fetch_from": "hr_perspective_3.weightage", "fieldname": "perspective_weightage_3", "fieldtype": "Percent", "label": "Perspective Weightage 3", "read_only": 1, "reqd": 1}, {"fieldname": "section_break_18", "fieldtype": "Section Break"}, {"fieldname": "kpi_details_3", "fieldtype": "Table", "label": "KPI Details 3", "options": "Goal Sheet Detail"}, {"fieldname": "section_break_20", "fieldtype": "Section Break"}, {"fieldname": "hr_perspective_4", "fieldtype": "Link", "label": "HR Perspective 4", "options": "HR Perspective", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_22", "fieldtype": "Column Break"}, {"fetch_from": "hr_perspective_4.weightage", "fieldname": "perspective_weightage_4", "fieldtype": "Percent", "label": "Perspective Weightage 4", "read_only": 1, "reqd": 1}, {"fieldname": "section_break_24", "fieldtype": "Section Break"}, {"fieldname": "kpi_details_4", "fieldtype": "Table", "label": "KPI Details 4", "options": "Goal Sheet Detail"}, {"fieldname": "section_break_26", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "total_employee_rating", "fieldtype": "Float", "label": "Total Employee Rating", "precision": "1", "read_only": 1}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "total_manager_rating", "fieldtype": "Float", "label": "Total Manager Rating", "permlevel": 1, "precision": "1", "read_only": 1}], "modified": "2020-01-12 15:40:12.210088", "modified_by": "Administrator", "module": "Payware", "name": "Employee KPI", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}