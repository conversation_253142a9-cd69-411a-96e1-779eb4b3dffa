{"creation": "2020-01-01 17:49:56.278695", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["objective", "measure", "target", "acheivement", "objective_weightage", "employee_rating", "manager_rating", "employee_remarks", "manager_remarks"], "fields": [{"columns": 2, "fieldname": "objective", "fieldtype": "Link", "in_list_view": 1, "label": "Objective", "options": "Designation Objective"}, {"columns": 2, "fieldname": "measure", "fieldtype": "Data", "in_list_view": 1, "label": "Measure"}, {"columns": 2, "fieldname": "target", "fieldtype": "Data", "in_list_view": 1, "label": "Target"}, {"columns": 2, "fieldname": "acheivement", "fieldtype": "Data", "label": "Acheivement"}, {"columns": 2, "fieldname": "objective_weightage", "fieldtype": "Percent", "label": "Objective Weightage"}, {"columns": 1, "fieldname": "employee_rating", "fieldtype": "Float", "in_list_view": 1, "label": "Employee Rating", "precision": "1"}, {"columns": 1, "fieldname": "manager_rating", "fieldtype": "Float", "in_list_view": 1, "label": "Manager Rating", "permlevel": 1, "precision": "1"}, {"columns": 1, "fieldname": "employee_remarks", "fieldtype": "Data", "in_list_view": 1, "label": "Employee Remarks"}, {"columns": 1, "fieldname": "manager_remarks", "fieldtype": "Data", "in_list_view": 1, "label": "Manager Remarks", "permlevel": 1}], "istable": 1, "modified": "2020-01-06 14:58:54.978291", "modified_by": "Administrator", "module": "Payware", "name": "Goal Sheet Detail", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}