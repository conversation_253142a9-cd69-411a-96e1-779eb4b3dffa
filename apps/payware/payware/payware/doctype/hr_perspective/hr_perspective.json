{"autoname": "field:perspective", "creation": "2020-01-01 17:36:35.381070", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["perspective", "weightage", "description"], "fields": [{"fieldname": "perspective", "fieldtype": "Data", "in_list_view": 1, "label": "Perspective", "reqd": 1, "unique": 1}, {"fieldname": "weightage", "fieldtype": "Percent", "in_list_view": 1, "label": "Weightage", "reqd": 1}, {"fieldname": "description", "fieldtype": "Data", "label": "Description"}], "modified": "2020-01-01 17:36:49.407826", "modified_by": "Administrator", "module": "Payware", "name": "HR Perspective", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}