{"autoname": "{PW}-{payroll_entry}-{payment_type}-", "creation": "2019-08-01 17:07:45.527511", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["payroll_entry", "start_date", "end_date", "payment_mode", "column_break_7", "payment_date", "payment_type", "payment_reference_number", "receipt_number", "amended_from"], "fields": [{"fieldname": "payroll_entry", "fieldtype": "Link", "label": "Payroll Entry", "options": "Payroll Entry"}, {"fieldname": "payment_type", "fieldtype": "Select", "label": "Payment Type", "options": "\nNSSF\nSDL\nWCF"}, {"fieldname": "payment_mode", "fieldtype": "Select", "label": "Payment Mode", "options": "\nCash\nBank\nTISS"}, {"fieldname": "payment_date", "fieldtype": "Date", "label": "Payment Date"}, {"description": "(Cheque Number, Control Number, Swift Ref. Number)", "fieldname": "payment_reference_number", "fieldtype": "Data", "label": "Payment Reference Number"}, {"fieldname": "receipt_number", "fieldtype": "Data", "label": "Receipt Number"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "start_date", "fieldtype": "Date", "label": "Start Date", "read_only": 1}, {"fieldname": "end_date", "fieldtype": "Data", "label": "End Date", "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Payroll Payment", "print_hide": 1, "read_only": 1}], "is_submittable": 1, "modified": "2020-06-10 21:08:15.242420", "modified_by": "Administrator", "module": "Payware", "name": "Payroll Payment", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}