{"actions": [], "creation": "2018-12-30 13:21:50.169110", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["overtime_section", "working_hours_per_month", "column_break_4", "ot_module", "account_settings_section", "default_account_for_additional_component_cash_journal", "biometric_attendace_section", "enable_biometric_attendance", "section_break_6", "p_o_box", "city", "street", "bank_account_for_tra", "column_break_11", "plot_number", "block_number", "skills_development_levy", "employer_registration_numbers_section", "social_security_fund_registration_number", "social_security_control_number", "column_break_12", "social_security_fund_registration_district", "wcf_workmen_compensation_fund_section", "wcf_registration_number"], "fields": [{"fieldname": "overtime_section", "fieldtype": "Section Break", "label": "Overtime"}, {"fieldname": "working_hours_per_month", "fieldtype": "Float", "label": "Working Hours per Month"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "ot_module", "fieldtype": "Check", "label": "OT Module"}, {"fieldname": "account_settings_section", "fieldtype": "Section Break", "label": "Account <PERSON><PERSON>"}, {"fieldname": "default_account_for_additional_component_cash_journal", "fieldtype": "Link", "label": "Default Account for additional component cash journal", "options": "Account"}, {"fieldname": "section_break_6", "fieldtype": "Section Break", "label": "Statutory Reports Information"}, {"fieldname": "p_o_box", "fieldtype": "Data", "label": "P O Box"}, {"fieldname": "city", "fieldtype": "Data", "label": "City"}, {"fieldname": "street", "fieldtype": "Data", "label": "Street"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "plot_number", "fieldtype": "Data", "label": "Plot Number"}, {"fieldname": "block_number", "fieldtype": "Data", "label": "Block Number"}, {"fieldname": "skills_development_levy", "fieldtype": "Percent", "label": "Skills Development Levy"}, {"fieldname": "employer_registration_numbers_section", "fieldtype": "Section Break", "label": "Social Security Details"}, {"fieldname": "social_security_fund_registration_number", "fieldtype": "Data", "label": "Social Security Employer Number"}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "social_security_fund_registration_district", "fieldtype": "Data", "label": "Social Security Registration District"}, {"fieldname": "wcf_workmen_compensation_fund_section", "fieldtype": "Section Break", "label": "WCF - Workmen Compensation Fund"}, {"fieldname": "wcf_registration_number", "fieldtype": "Data", "label": "WCF Registration Number"}, {"fieldname": "biometric_attendace_section", "fieldtype": "Section Break", "label": "Biometric Attendace"}, {"default": "0", "fieldname": "enable_biometric_attendance", "fieldtype": "Check", "label": "Enable Biometric Attendance"}, {"fieldname": "bank_account_for_tra", "fieldtype": "Link", "label": "Bank Account for TRA", "options": "Bank Account"}, {"fieldname": "social_security_control_number", "fieldtype": "Data", "label": "Social Security Control Number"}], "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2021-07-09 16:58:05.578372", "modified_by": "Administrator", "module": "Payware", "name": "Payware Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}