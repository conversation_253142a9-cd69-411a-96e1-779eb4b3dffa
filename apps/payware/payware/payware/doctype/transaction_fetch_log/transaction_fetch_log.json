{"creation": "2020-01-07 03:30:41.924895", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["status", "start_time", "end_time", "column_break_4", "count", "page", "unique", "repeated", "log_section", "log"], "fields": [{"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "\nSuccess\nError", "read_only": 1}, {"fieldname": "start_time", "fieldtype": "Datetime", "in_list_view": 1, "label": "Start Time", "read_only": 1}, {"fieldname": "end_time", "fieldtype": "Datetime", "in_list_view": 1, "label": "End Time", "read_only": 1}, {"fieldname": "count", "fieldtype": "Data", "in_list_view": 1, "label": "Count", "read_only": 1}, {"fieldname": "log", "fieldtype": "Long Text", "label": "Log", "read_only": 1}, {"fieldname": "page", "fieldtype": "Data", "in_list_view": 1, "label": "Page", "read_only": 1}, {"fieldname": "unique", "fieldtype": "Data", "label": "Unique Record", "read_only": 1}, {"fieldname": "repeated", "fieldtype": "Data", "label": "Repeated Record", "read_only": 1}, {"fieldname": "log_section", "fieldtype": "Section Break", "label": "Log"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}], "modified": "2020-01-07 23:07:46.449740", "modified_by": "<EMAIL>", "module": "Payware", "name": "Transaction Fetch Log", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}