{"creation": "2020-01-07 00:13:59.393543", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["status", "id", "emp_code", "punch_time", "punch_state", "verify_type", "work_code", "terminal_sn", "terminal_alias", "area_alias", "longitude", "latitude", "gps_location", "mobile", "column_break_14", "source", "purpose", "crc", "is_attendance", "reserved", "upload_time", "sync_status", "sync_time", "emp", "terminal", "transaction_fetch_log", "employee_checkin"], "fields": [{"fieldname": "id", "fieldtype": "Data", "in_list_view": 1, "label": "ID", "read_only": 1}, {"fieldname": "emp_code", "fieldtype": "Data", "in_list_view": 1, "label": "Emp Code", "read_only": 1}, {"fieldname": "punch_time", "fieldtype": "Data", "in_list_view": 1, "label": "Punch Time", "read_only": 1}, {"fieldname": "punch_state", "fieldtype": "Data", "in_list_view": 1, "label": "Punch State", "read_only": 1}, {"fieldname": "verify_type", "fieldtype": "Data", "label": "Verify Type", "read_only": 1}, {"fieldname": "work_code", "fieldtype": "Data", "label": "Work Code", "read_only": 1}, {"fieldname": "terminal_sn", "fieldtype": "Data", "label": "Terminal SN", "read_only": 1}, {"fieldname": "terminal_alias", "fieldtype": "Data", "label": "Terminal Alias", "read_only": 1}, {"fieldname": "area_alias", "fieldtype": "Data", "label": "Area Alias", "read_only": 1}, {"fieldname": "longitude", "fieldtype": "Data", "label": "Longitude", "read_only": 1}, {"fieldname": "latitude", "fieldtype": "Data", "label": "Latitude", "read_only": 1}, {"fieldname": "gps_location", "fieldtype": "Data", "label": "GPS Location", "read_only": 1}, {"fieldname": "mobile", "fieldtype": "Data", "label": "Mobile", "read_only": 1}, {"fieldname": "source", "fieldtype": "Data", "label": "Source", "read_only": 1}, {"fieldname": "purpose", "fieldtype": "Data", "label": "Purpose", "read_only": 1}, {"fieldname": "crc", "fieldtype": "Data", "label": "CRC", "read_only": 1}, {"fieldname": "is_attendance", "fieldtype": "Data", "label": "Is Attendance", "read_only": 1}, {"fieldname": "reserved", "fieldtype": "Data", "label": "Reserved", "read_only": 1}, {"fieldname": "upload_time", "fieldtype": "Data", "label": "Upload Time", "read_only": 1}, {"fieldname": "sync_status", "fieldtype": "Data", "label": "Sync Status", "read_only": 1}, {"fieldname": "sync_time", "fieldtype": "Data", "label": "Sync Time", "read_only": 1}, {"fieldname": "emp", "fieldtype": "Data", "in_list_view": 1, "label": "Emp", "read_only": 1}, {"fieldname": "terminal", "fieldtype": "Data", "label": "Terminal", "read_only": 1}, {"fieldname": "transaction_fetch_log", "fieldtype": "Link", "label": "Transaction Fetch Log", "options": "Transaction Fetch Log", "read_only": 1}, {"fieldname": "employee_checkin", "fieldtype": "Link", "label": "Employee Checkin", "options": "Employee Checkin", "read_only": 1}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "\nLinked\nWaiting\nError", "read_only": 1}], "modified": "2020-01-09 13:48:29.455490", "modified_by": "<EMAIL>", "module": "Payware", "name": "Transactions Log", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "title_field": "emp_code", "track_changes": 1}