{"align_labels_right": 0, "creation": "2020-04-03 10:19:26.959893", "css": ".print-format {\r\n    margin-left: -0mm !important;\r\n    margin-right: -0mm !important;\r\n    margin-top: 0mm !important;\r\n    margin-bottom: 0mm !important;\r\n    padding-top: 5mm !important;\r\n}\r\n\r\nbody {\r\n        margin: 0 !important;\r\n        border: 0 !important;\r\n        padding: 0mm 0mm 0mm !important;\r\n }\r\n\r\n.print-format td, .print-format th {\r\n    vertical-align: top !important;\r\n    padding: 1px !important;\r\n}\r\n\r\nh1, .h1, h2, .h2, h3, .h3 {\r\n    margin-top: 0px;\r\n    margin-bottom: 0px;\r\n}\r\n\r\nhr {\r\n    margin-top: 3px;\r\n    margin-bottom: 3px;\r\n    border: 0;\r\n    border-top: 2px solid #d1d8dd;\r\n}", "custom_format": 0, "disabled": 0, "doc_type": "Loan", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<br>\\r\\n<table style=\\\"width: 100%;\\\">\\r\\n<tbody>\\r\\n<tr>\\r\\n<td style=\\\"vertical-align: bottom !important;\\\">\\r\\n<h2><strong>LOAN AGREEMENT</strong></h2>\\r\\n</td>\\r\\n<td style=\\\"text-align: right; vertical-align: bottom !important;\\\">\\r\\n    {{ doc.name }}\\r\\n</td>\\r\\n</tr>\\r\\n</tbody>\\r\\n</table>\\r\\n<hr>\"}, {\"label\": \"\", \"fieldtype\": \"Section Break\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"applicant_name\", \"label\": \"Applicant Name\", \"print_hide\": 0}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"posting_date\", \"label\": \"Posting Date\", \"print_hide\": 0}, {\"label\": \"Loan Details\", \"fieldtype\": \"Section Break\"}, {\"fieldtype\": \"Column Break\"}, {\"align\": \"left\", \"fieldname\": \"loan_amount\", \"label\": \"Loan Amount\", \"print_hide\": 0}, {\"fieldname\": \"disbursement_date\", \"label\": \"Disbursement Date\", \"print_hide\": 0}, {\"align\": \"left\", \"fieldname\": \"repayment_start_date\", \"label\": \"Repayment Date\", \"print_hide\": 0}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"repayment_method\", \"label\": \"Repayment Method\", \"print_hide\": 0}, {\"align\": \"left\", \"fieldname\": \"repayment_periods\", \"label\": \"Repayment Months\", \"print_hide\": 0}, {\"align\": \"left\", \"fieldname\": \"monthly_repayment_amount\", \"label\": \"Monthly Amount\", \"print_hide\": 0}, {\"label\": \"Account Info\", \"fieldtype\": \"Section Break\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"mode_of_payment\", \"label\": \"Mode of Payment\", \"print_hide\": 0}, {\"fieldtype\": \"Column Break\"}, {\"label\": \"Repayment Schedule\", \"fieldtype\": \"Section Break\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"repayment_schedule\", \"label\": \"Repayment Schedule\", \"visible_columns\": [{\"print_width\": \"\", \"fieldname\": \"payment_date\", \"print_hide\": 0}, {\"print_width\": \"\", \"fieldname\": \"total_payment\", \"print_hide\": 0}, {\"print_width\": \"\", \"fieldname\": \"balance_loan_amount\", \"print_hide\": 0}], \"print_hide\": 0}, {\"label\": \"\", \"fieldtype\": \"Section Break\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldtype\": \"HTML\", \"fieldname\": \"_custom_html\", \"label\": \"Custom HTML\", \"options\": \"<table style=\\\"float: left;\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\">\\r\\n<tbody>\\r\\n<tr>\\r\\n<td colspan=\\\"2\\\">\\r\\n<p>I _________________________________________________ verify that the above loan and deduction details are correct. I authorise my Employer to deduct the loan amount in installments as mentioned above from my monthly pay. In the event I resign or my contract is terminated I authorise my Employer to deduct the full amount due from my termination pay or salary</p>\\r\\n</td>\\r\\n</tr>\\r\\n<tr>\\r\\n<td style=\\\"text-align: center;\\\">\\r\\n<p>&nbsp;</p>\\r\\n<p>_____________________________</p>\\r\\n</td>\\r\\n<td style=\\\"text-align: center;\\\">\\r\\n<p>&nbsp;</p>\\r\\n<p>_____________________________</p>\\r\\n</td>\\r\\n</tr>\\r\\n<tr>\\r\\n<td style=\\\"text-align: center;\\\">Signature</td>\\r\\n<td style=\\\"text-align: center;\\\">Date</td>\\r\\n</tr>\\r\\n<tr>\\r\\n<td style=\\\"text-align: left;\\\" colspan=\\\"2\\\">\\r\\n    <hr>\\r\\n    We, the undersigned Gurantors agree to pay the balance loan as per the above schedule from our salaries should the employee fail to pay his loan</td>\\r\\n</tr>\\r\\n<tr>\\r\\n<td style=\\\"text-align: center;\\\">\\r\\n<p>&nbsp;</p>\\r\\n<p>_____________________________</p>\\r\\n</td>\\r\\n<td style=\\\"text-align: center;\\\">\\r\\n<p>&nbsp;</p>\\r\\n<p>_____________________________</p>\\r\\n</tr>\\r\\n<tr>\\r\\n<td style=\\\"text-align: center;\\\">First Gurantrotor Namesm, Signature and Date</td>\\r\\n<td style=\\\"text-align: center;\\\">Second Gurantrotor Namesm, Signature and Date</td>\\r\\n</tr>\\r\\n</tbody>\\r\\n</table>\", \"print_hide\": 0}]", "idx": 0, "line_breaks": 0, "modified": "2020-04-06 10:20:30.550369", "modified_by": "Administrator", "module": "Payware", "name": "Payware Loan Agreement", "owner": "Administrator", "print_format_builder": 1, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}