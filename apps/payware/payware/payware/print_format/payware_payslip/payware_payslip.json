{"align_labels_right": 0, "creation": "2020-03-04 10:07:03.411539", "css": ".print-format {\n    margin-left: -0mm !important;\n    margin-right: -0mm !important;\n    margin-top: 0mm !important;\n    margin-bottom: 0mm !important;\n    padding-top: 5mm !important;\n}\n\nbody {\n        margin: 0 !important;\n        border: 0 !important;\n        padding: 0mm 0mm 0mm !important;\n }\n      \n.data-field {\n    margin-top: 0px !important;\n    margin-bottom: 0px !important;\n}\n.print-format th {\n    vertical-align: top !important;\n    padding: 2px !important;\n}\n\n.print-format td {\n    vertical-align: top !important;\n    padding: 2px !important;\n}\n\n.print-heading {\n  display: none;\n}\n\n", "custom_format": 0, "disabled": 0, "doc_type": "<PERSON><PERSON>", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON>", "format_data": "[{\"options\": \"<div class=\\\"print-heading\\\">\\n    <h2><PERSON><PERSON> Slip<br><small>{{ doc.name }}</h2>\\n</div>\", \"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\"}, {\"label\": \"\", \"fieldtype\": \"Section Break\"}, {\"fieldtype\": \"Column Break\"}, {\"options\": \"<h1 class=\\\"text-center\\\" style=\\\"font-size: 22px;\\\"><b>{{ doc.company }}</b></h1>\\n<h2 class=\\\"text-center\\\">Payslip</h2>\", \"fieldname\": \"_custom_html\", \"print_hide\": 0, \"fieldtype\": \"HTML\", \"label\": \"Custom HTML\"}, {\"fieldtype\": \"Column Break\"}, {\"options\": \"<h1 class=\\\"text-center\\\" style=\\\"font-size: 22px;\\\"><b>{{ doc.company }}</b></h1>\\n<h2 class=\\\"text-center\\\">Payslip</h2>\", \"fieldname\": \"_custom_html\", \"print_hide\": 0, \"fieldtype\": \"HTML\", \"label\": \"Custom HTML\"}, {\"label\": \"\", \"fieldtype\": \"Section Break\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"employee\", \"print_hide\": 0, \"align\": \"left\", \"label\": \"Employee ID\"}, {\"fieldname\": \"employee_name\", \"print_hide\": 0, \"label\": \"Employee Name\"}, {\"fieldname\": \"department\", \"print_hide\": 0, \"label\": \"Department\"}, {\"fieldname\": \"designation\", \"print_hide\": 0, \"label\": \"Designation\"}, {\"fieldname\": \"total_working_days\", \"print_hide\": 0, \"label\": \"Working Days\"}, {\"fieldname\": \"leave_without_pay\", \"print_hide\": 0, \"label\": \"Leave Without Pay\"}, {\"fieldname\": \"payment_days\", \"print_hide\": 0, \"label\": \"Payment Days\"}, {\"fieldname\": \"start_date\", \"print_hide\": 0, \"label\": \"Start Date\"}, {\"fieldname\": \"end_date\", \"print_hide\": 0, \"label\": \"End Date\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"employee\", \"print_hide\": 0, \"align\": \"left\", \"label\": \"Employee ID\"}, {\"fieldname\": \"employee_name\", \"print_hide\": 0, \"label\": \"Employee Name\"}, {\"fieldname\": \"department\", \"print_hide\": 0, \"label\": \"Department\"}, {\"fieldname\": \"designation\", \"print_hide\": 0, \"label\": \"Designation\"}, {\"fieldname\": \"total_working_days\", \"print_hide\": 0, \"label\": \"Working Days\"}, {\"fieldname\": \"leave_without_pay\", \"print_hide\": 0, \"label\": \"Leave Without Pay\"}, {\"fieldname\": \"payment_days\", \"print_hide\": 0, \"label\": \"Payment Days\"}, {\"fieldname\": \"start_date\", \"print_hide\": 0, \"label\": \"Start Date\"}, {\"fieldname\": \"end_date\", \"print_hide\": 0, \"label\": \"End Date\"}, {\"label\": \"Earnings\", \"fieldtype\": \"Section Break\"}, {\"fieldtype\": \"Column Break\"}, {\"options\": \"<strong>Earnings</strong>\\n<table class=\\\"table table-bordered table-condensed\\\">\\n    <colgroup>\\n       <col width=\\\"5%\\\">\\n       <col width=\\\"55%\\\">\\n       <col width=\\\"40%\\\">\\n    </colgroup>\\n    <thead>\\n        <tr>\\n            <th>Sr</th>\\n            <th>Component</th>\\n            <th class=\\\"text-right\\\" data-fieldname=\\\"earnings\\\" data-fieldtype=\\\"Table\\\">Amount</th>\\n        </tr>\\n    </thead>\\n    <tbody>\\n        {% for item in doc.earnings %}\\n        {% if item.do_not_include_in_total == 0 %}\\n        <tr>\\n            <td>{{ loop.index }}</td>\\n            <td>{{ item.salary_component }}</td>\\n            <td class=\\\"text-right\\\">{{ item.get_formatted(\\\"amount\\\", doc) }}\\n            </td>\\n        </tr>\\n        {% endif %}\\n        {% endfor %}\\n    </tbody>\\n        <tr>\\n            <th></th>\\n            <th>Total Earnings</th>\\n            <th class=\\\"text-right\\\">{{ doc.get_formatted(\\\"gross_pay\\\", doc) }}</th>\\n        </tr> \\n    <tfooter>\\n    </tfooter>\\n</table>\\n\\n<strong>Deductions</strong>\\n<table class=\\\"table table-bordered table-condensed\\\">\\n    <colgroup>\\n       <col width=\\\"5%\\\">\\n       <col width=\\\"55%\\\">\\n       <col width=\\\"40%\\\">\\n    </colgroup>\\n    <thead>\\n        <tr>\\n            <th>Sr</th>\\n            <th>Component</th>\\n            <th class=\\\"text-right\\\" data-fieldname=\\\"earnings\\\" data-fieldtype=\\\"Table\\\">Amount</th>\\n        </tr>\\n    </thead>\\n    <tbody>\\n        {% for item in doc.deductions %}\\n        {% if item.do_not_include_in_total == 0 %}\\n        <tr>\\n            <td>{{ loop.index }}</td>\\n            <td>{{ item.salary_component }}</td>\\n            <td class=\\\"text-right\\\">{{ item.get_formatted(\\\"amount\\\", doc) }}\\n            </td>\\n        </tr>\\n        {% endif %}\\n        {% endfor %}\\n    </tbody>\\n        <tr>\\n            <th></th>\\n            <th>Total Deductions</th>\\n            <th class=\\\"text-right\\\">{{ doc.get_formatted(\\\"total_deduction\\\", doc) }}</th>\\n        </tr> \\n    <tfooter>\\n    </tfooter>\\n</table>\", \"fieldname\": \"_custom_html\", \"print_hide\": 0, \"fieldtype\": \"HTML\", \"label\": \"Custom HTML\"}, {\"fieldname\": \"total_loan_repayment\", \"print_hide\": 0, \"label\": \"Total Loan Repayment\"}, {\"fieldname\": \"net_pay\", \"print_hide\": 0, \"label\": \"Net Pay\"}, {\"fieldname\": \"total_in_words\", \"print_hide\": 0, \"label\": \"Total in words\"}, {\"fieldtype\": \"Column Break\"}, {\"options\": \"<strong>Earnings</strong>\\n<table class=\\\"table table-bordered table-condensed\\\">\\n    <colgroup>\\n       <col width=\\\"5%\\\">\\n       <col width=\\\"55%\\\">\\n       <col width=\\\"40%\\\">\\n    </colgroup>\\n    <thead>\\n        <tr>\\n            <th>Sr</th>\\n            <th>Component</th>\\n            <th class=\\\"text-right\\\" data-fieldname=\\\"earnings\\\" data-fieldtype=\\\"Table\\\">Amount</th>\\n        </tr>\\n    </thead>\\n    <tbody>\\n        {% for item in doc.earnings %}\\n        {% if item.do_not_include_in_total == 0 %}\\n        <tr>\\n            <td>{{ loop.index }}</td>\\n            <td>{{ item.salary_component }}</td>\\n            <td class=\\\"text-right\\\">{{ item.get_formatted(\\\"amount\\\", doc) }}\\n            </td>\\n        </tr>\\n        {% endif %}\\n        {% endfor %}\\n    </tbody>\\n        <tr>\\n            <th></th>\\n            <th>Total Earnings</th>\\n            <th class=\\\"text-right\\\">{{ doc.get_formatted(\\\"gross_pay\\\", doc) }}</th>\\n        </tr> \\n    <tfooter>\\n    </tfooter>\\n</table>\\n\\n<strong>Deductions</strong>\\n<table class=\\\"table table-bordered table-condensed\\\">\\n    <colgroup>\\n       <col width=\\\"5%\\\">\\n       <col width=\\\"55%\\\">\\n       <col width=\\\"40%\\\">\\n    </colgroup>\\n    <thead>\\n        <tr>\\n            <th>Sr</th>\\n            <th>Component</th>\\n            <th class=\\\"text-right\\\" data-fieldname=\\\"earnings\\\" data-fieldtype=\\\"Table\\\">Amount</th>\\n        </tr>\\n    </thead>\\n    <tbody>\\n        {% for item in doc.deductions %}\\n        {% if item.do_not_include_in_total == 0 %}\\n        <tr>\\n            <td>{{ loop.index }}</td>\\n            <td>{{ item.salary_component }}</td>\\n            <td class=\\\"text-right\\\">{{ item.get_formatted(\\\"amount\\\", doc) }}\\n            </td>\\n        </tr>\\n        {% endif %}\\n        {% endfor %}\\n    </tbody>\\n        <tr>\\n            <th></th>\\n            <th>Total Deductions</th>\\n            <th class=\\\"text-right\\\">{{ doc.get_formatted(\\\"total_deduction\\\", doc) }}</th>\\n        </tr> \\n    <tfooter>\\n    </tfooter>\\n</table>\", \"fieldname\": \"_custom_html\", \"print_hide\": 0, \"fieldtype\": \"HTML\", \"label\": \"Custom HTML\"}, {\"fieldname\": \"total_loan_repayment\", \"print_hide\": 0, \"label\": \"Total Loan Repayment\"}, {\"fieldname\": \"net_pay\", \"print_hide\": 0, \"label\": \"Net Pay\"}, {\"fieldname\": \"total_in_words\", \"print_hide\": 0, \"label\": \"Total in words\"}, {\"label\": \"\", \"fieldtype\": \"Section Break\"}, {\"fieldtype\": \"Column Break\"}, {\"options\": \"<div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\n   <div class=\\\"col-xs-5\\\">\\n      <br><br><br><label>Signature:</label>\\n   </div>\\n   <div class=\\\"col-xs-7 value\\\">\\n      <br><br><br>_______________________\\n   </div>\\n</div>\", \"fieldname\": \"_custom_html\", \"print_hide\": 0, \"fieldtype\": \"HTML\", \"label\": \"Custom HTML\"}, {\"fieldtype\": \"Column Break\"}]", "idx": 0, "line_breaks": 0, "modified": "2020-03-30 18:26:40.126207", "modified_by": "Administrator", "module": "Payware", "name": "Payware Payslip", "owner": "Administrator", "parent": "<PERSON><PERSON>", "print_format_builder": 1, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}