<style>
.print-format {
        padding-left: 15mm !important;;
        padding-right: 15mm !important;;
        padding-top: 0mm;
		font-size: 10pt;	
}
.print-format td, .print-format th {
    vertical-align: top !important;
    padding: 3px !important;
}

@media screen {
	.print-format {
		padding-left: 15mm !important;;
        padding-right: 15mm !important;;
        padding-top: 0mm;
		font-size: 10pt;
		width: 8.5in;
		height: 11in;
		}
	
	.print-format td, .print-format th {
    vertical-align: top !important;
    padding: 3px !important;
	}
}
</style>

{%	
	var from_date = filters.from_date; 
	var to_date = filters.to_date;
	var total_rows = data.length - 1;
	var total_net_pay = data[total_rows][ __("Net Pay")];

	var net_pay_in_words = "";

	frappe.call({
		method: "payware.payware.utils.money_in_words",
		args: {
			number: total_net_pay
		},
		async:false,
		callback: function(r){
			if (r.message){
				net_pay_in_words = r.message;
			}
		}
	});
%}

<P ALIGN=LEFT>Ref. No. {%= data[0][ __("Payroll Entry")] %}</P>
<P ALIGN=RIGHT>Date: {%= frappe.datetime.global_date_format(data[0][ __("Cheque Date")]) %}</P>
<P ALIGN=LEFT>
To,<br>
Branch Manager,<br>
{%= data[0][ __("Bank Name")] %},<br>
{%= data[0][ __("Branch")] %} Branch,<br>
{%= data[0][ __("Address1")] %},<br>
{%= data[0][ __("City")] %}, {%= data[0][ __("Country")] %}.<br>
<br>
Dear Sir,<br><br>

<u><b>Sub: Credit of Salaries of Employees to their respective Saving Accounts for the month of {%= data[0][ __("Month")] %}</b></u><br><br>

Kindly refer to the subject matter. For the same kindly find attached the following statement showing the amount to be credited to the employees account maintained with you. We are enclosing Cheque Number <u>&nbsp;{%= data[0][ __("Cheque No.")] %}&nbsp;</u> dated <u>&nbsp;{%= frappe.datetime.global_date_format(data[0][ __("Cheque Date")]) %}&nbsp;</u> drawn favoring yourself for the {%= format_currency(total_net_pay) %} [{%= net_pay_in_words %}].<br><br>

<table class="table table-bordered">
	<colgroup>
		<col style="width: 5%">
		<col style="width: 20%">
		<col style="width: 40%">
		<col style="width: 15%">
		<col style="width: 20%">
	</colgroup>
	<thead>
		<tr>
			<th style="text-align: center">{%= __("SR No.") %}</th>
			<th style="text-align: center">{%= __("Employee ID") %}</th>
			<th style="text-align: center">{%= __("Employee Name") %}</th>
			<th style="text-align: center">{%= __("Account No.") %}</th>
			<th style="text-align: center">{%= __("Net Pay") %}</th>
		</tr>
	</thead>
	<tbody>
		{% for(var i=0, l=data.length; i<l-1; i++) { %}
		<tr>
            <td>{%= i+1 %}</td>
            <td>{%= data[i][ __("Employee ID")] %}</td>
            <td>{%= data[i][ __("Employee Name")] %}</td>
            <td>{%= data[i][ __("Account No.")] %}</td>
            <td style="text-align: right">{%= data[i][ __("Net Pay")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %} </td>
        </tr>
		{% } %}
	</tbody>
	<tfoot>
		<tr>
			<th style="text-align: right" colspan=4><b>TOTAL</b></td>
			<th style="text-align: right">{%= total_net_pay.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %} </th>		
        </tr>
	</tfoot>
</table>
<br><br>
Kindly affect the transfers today only.<br><br>
For and on behalf of<br>
{%= data[0][ __("Company")] %}<br><br><br><br>
________________________________<br>
Authorised Signatory<br>