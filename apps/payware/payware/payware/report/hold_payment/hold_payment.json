{"add_total_row": 1, "creation": "2019-08-01 12:30:28.719491", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "json": "{\"order_by\": \"`tab<PERSON><PERSON><PERSON> Slip`.`modified` desc\", \"add_total_row\": 1, \"fields\": [[\"posting_date\", \"Salary Slip\"], [\"employee\", \"Salary Slip\"], [\"employee_name\", \"Salary Slip\"], [\"docstatus\", \"Salary Slip\"], [\"net_pay\", \"Salary Slip\"]], \"column_widths\": {\"employee\": 208, \"posting_date\": 91, \"employee_name\": 110, \"name\": 197, \"net_pay\": 200}, \"add_totals_row\": 0, \"filters\": [], \"page_length\": 20}", "modified": "2019-08-01 12:34:24.198978", "modified_by": "Administrator", "module": "Payware", "name": "Hold Payment", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "<PERSON><PERSON>", "report_name": "Hold Payment", "report_type": "Report Builder", "roles": [{"role": "HR Manager"}, {"role": "HR User"}, {"role": "Employee"}, {"role": "System Manager"}]}