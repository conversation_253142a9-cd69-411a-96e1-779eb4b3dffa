{"add_total_row": 0, "creation": "2019-06-21 15:32:51.712217", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2019-06-21 15:45:17.198809", "modified_by": "Administrator", "module": "Payware", "name": "ITX219.01.E - SDL Monthly Return", "owner": "Administrator", "prepared_report": 0, "query": "SELECT\tsum(gross_pay) as \"Gross Pay:Currency:\",\n\tsum(sd.amount) as \"SDL:Currency:\",\n\tss.company as \"Company:Data:0\",\n\tcmp.tax_id as \"TIN:Data:0\",\n\tcmp.email as \"Email:Data:0\",\n\tcmp.phone_no as \"Phone:Data:0\",\n\tcmp.fax as \"Fax Number:Data:0\",\n\tcmp.p_o_box as \"P O Box:Data:0\",\n\tcmp.street as \"Street:Data:0\",\n\tcmp.plot_number as \"Plot Number:Data:0\",\n\tcmp.block_number as \"Block Number:Data:0\",\n\tcmp.city as \"City:Data:0\"\nFROM `tabSalary Slip` ss  LEFT OUTER JOIN `tabSalary Detail` sd\tON sd.parent = ss.name \n\t\t\t  LEFT OUTER JOIN `tabCompany` cmp \tON ss.company = cmp.name \nWHERE\tss.start_date >= %(from_date)s\nAND\tss.end_date <= %(to_date)s\nORDER BY ss.start_date\n", "ref_doctype": "<PERSON><PERSON>", "report_name": "ITX219.01.E - SDL Monthly Return", "report_type": "Query Report", "roles": [{"role": "HR Manager"}, {"role": "HR User"}, {"role": "Employee"}, {"role": "System Manager"}]}