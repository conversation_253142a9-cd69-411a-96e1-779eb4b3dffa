<style>
	.print-format {
		padding-left: 10mm;
		padding-right: 5mm;
		padding-top: 0mm;
		font-size: 14pt;
	}

	.float-container {
		border: 1px solid black padding: 0px;
	}

	.float-child {
		float: left;
		padding: 3px;

	}

	@media screen {
		.print-format {
			width: 8.5in;
			height: 11in;
		}
	}
</style>

{% var start_date = filters.from_date %}
{% var end_date = filters.to_date %}
{% var tin = data[0][ __("tax_id")] %}

{% if ( end_date[5]+ end_date[6] == "06") { %}
{% var tick1 = "X" %}
{% } %}

{% if ( end_date[5]+ end_date[6] == "12") { %}
{% var tick2 = "X" %}
{% } %}

<p class="manifest-header text-center">
	<img src="/files/tra-logo.gif" style="width: 35%;" />
</p>

<h3 class="manifest-header text-center">
	<br>P.A.Y.E.<br>
	STATEMENT AND PAYMENT OF TAX WITHHELD
</h3>
<h4>
	<table cellspacing=0 cellpadding=0 style="border: 0px solid black;">
		<colgroup>
			<col style="width: 50%" />
			<col style="width: 50%" />
		</colgroup>
		<tbody>
			<tr>
				<td></td>
				<td>
					<table>
						<colgroup>
							<col width="70">
							<col width="35">
							<col width="35">
							<col width="35">
							<col width="35">
						</colgroup>
						<tbody>
							<tr>
								<td>YEAR</td>
								<td style="border: 1px solid black; text-align: center;">{%= start_date[0] %}</td>
								<td style="border: 1px solid black; text-align: center;">{%= start_date[1] %}</td>
								<td style="border: 1px solid black; text-align: center;">{%= start_date[2] %}</td>
								<td style="border: 1px solid black; text-align: center;">{%= start_date[3] %}</td>
							</tr>
						</tbody>
					</table>
					<br>
					<table>
						<colgroup>
							<col width="70">
							<col width="30">
							<col width="30">
							<col width="30">
							<col width="30">
							<col width="30">
							<col width="30">
							<col width="30">
							<col width="30">
							<col width="30">
							<col width="30">
							<col width="30">
						</colgroup>
						<tbody>
							<tr>
								<td>TIN</td>
								<td style="border: 1px solid black; text-align: center;">{%= tin[0] %}</td>
								<td style="border: 1px solid black; text-align: center;">{%= tin[1] %}</td>
								<td style="border: 1px solid black; text-align: center;">{%= tin[2] %}</td>
								<td style="border: 1px solid black; text-align: center; background-color: black;"></td>
								<td style="border: 1px solid black; text-align: center;">{%= tin[4] %}</td>
								<td style="border: 1px solid black; text-align: center;">{%= tin[5] %}</td>
								<td style="border: 1px solid black; text-align: center;">{%= tin[6] %}</td>
								<td style="border: 1px solid black; text-align: center; background-color: black;"></td>
								<td style="border: 1px solid black; text-align: center;">{%= tin[8] %}</td>
								<td style="border: 1px solid black; text-align: center;">{%= tin[9] %}</td>
								<td style="border: 1px solid black; text-align: center;">{%= tin[10] %}</td>
							</tr>
						</tbody>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<b>Period</b> (Please tick appropriate)<br>
					<table>
						<colgroup>
							<col width="30">
							<col width="250">
						</colgroup>
						<tbody>
							<tr>
								<td style="border: 1px solid black; text-align: center;">{%= tick1 %}</td>
								<td><b>From 1 January to 30 June</b></td>
							</tr>
						</tbody>
					</table>
					<table>
						<colgroup>
							<col width="30">
							<col width="250">
						</colgroup>
						<tbody>
							<tr>
								<td style="border: 1px solid black; text-align: center;">{%= tick2 %}</td>
								<td><b>From 1 June to 31 December</b></td>
							</tr>
						</tbody>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					Name of Employer
					<div style="border: 1px solid black; width: 100%; padding: 5px;">
						{%= data[0][ __("company")] %}
					</div>
				</td>
			</tr>
			<tr>
				<td>
					Postal Address<br>
					<div class="float-container">
						<div class="float-child" style="width: 40%;">
							<div class="green">P. O. Box</div>
						</div>
						<div class="float-child" style="width: 60%; border: 1px solid black;">
							<div class="blue"> {%= data[0][ __("pobox")] %}</div>
						</div>
					</div>
				</td>
				<td>
					&nbsp<br>
					<div class="float-container">
						<div class="float-child" style="width: 40%;">
							<div class="green">Postal City</div>
						</div>
						<div class="float-child" style="width: 60%; border: 1px solid black;">
							<div class="blue"> {%= data[0][ __("city")] %}</div>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					Contact Number<br>
					<div class="float-container">
						<div class="float-child" style="width: 40%;">
							<div class="green">Phone number</div>
						</div>
						<div class="float-child" style="width: 60%; border: 1px solid black;">
							<div class="blue"> {%= data[0][ __("phone_no")] %}</div>
						</div>
					</div>
				</td>
				<td>
					&nbsp<br>
					<div class="float-container">
						<div class="float-child" style="width: 40%;">
							<div class="green">Second Phone</div>
						</div>
						<div class="float-child" style="width: 60%; border: 1px solid black;">
							<div class="blue" style="width: 250px;">&nbsp;</div>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="float-container">
						<div class="float-child" style="width: 40%;">
							<div class="green">Third number</div>
						</div>
						<div class="float-child" style="width: 60%; border: 1px solid black;">
							<div class="blue" style="width: 250px;">&nbsp;</div>
						</div>
					</div>
				</td>
				<td>
					<div class="float-container">
						<div class="float-child" style="width: 40%;">
							<div class="green">Fax number</div>
						</div>
						<div class="float-child" style="width: 60%; border: 1px solid black;">
							<div class="blue" style="width: 250px;">&nbsp;</div>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div class="float-container">
						<div class="float-child" style="width: 20%;">
							<div class="green">email address</div>
						</div>
						<div class="float-child" style="width: 80%; border: 1px solid black;">
							<div class="blue" style="width: 400px;">{%= data[0][ __("email")] %}</div>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					Physica Address<br>
					<div class="float-container">
						<div class="float-child" style="width: 40%;">
							<div class="green">Plot Number</div>
						</div>
						<div class="float-child" style="width: 60%; border: 1px solid black;">
							<div class="blue"> {%= data[0][ __("plot_no")] %}</div>
						</div>
					</div>
				</td>
				<td>
					&nbsp<br>
					<div class="float-container">
						<div class="float-child" style="width: 40%;">
							<div class="green">Block Number</div>
						</div>
						<div class="float-child" style="width: 60%; border: 1px solid black;">
							<div class="blue"> {%= data[0][ __("block_no")] %}</div>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div class="float-container">
						<div class="float-child" style="width: 20%;">
							<div class="green">Street/Location</div>
						</div>
						<div class="float-child" style="width: 80%; border: 1px solid black;">
							<div class="blue" style="width: 400px;">{%= data[0][ __("street")] %}</div>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div class="float-container">
						<div class="float-child" style="width: 20%;">
							<div class="green">Name of Branch</div>
						</div>
						<div class="float-child" style="width: 80%; border: 1px solid black;">
							<div class="blue" style="width: 400px;">&nbsp;</div>
						</div>
					</div>
				</td>
			</tr>
		</tbody>
	</table>
</h4>