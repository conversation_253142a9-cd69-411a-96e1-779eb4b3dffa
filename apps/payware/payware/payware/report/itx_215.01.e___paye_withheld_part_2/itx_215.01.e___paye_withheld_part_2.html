<style>
	.print-format {
		padding-left: 5mm;
		padding-right: 5mm;
		padding-top: 0mm;
		font-size: 8.5pt;
	}

	.print-format td,
	.print-format th {
		vertical-align: top !important;
		padding: 2px !important;

	}

	.table-bordered>thead>tr>th.no-line {
		border: none !important;
		border-right: none !important;
		border-left: none !important;
		border-top: none !important;
		border-right-style: none !important;
		border-left-style: none !important;
	}

	.table-bordered {
		border: none;
	}

	@media screen {
		.print-format {
			height: 8.5in;
			width: 11in;
			font-size: 8.5pt;
		}

		.print-format td,
		.print-format th {
			vertical-align: top !important;
			padding: 2px !important;

		}

		.table-bordered>thead>tr>th.no-line,
		td.no-line {
			border: none !important;
			border-right: none !important;
			border-left: none !important;
			border-top: none !important;
			border-right-style: none !important;
			border-left-style: none !important;
		}

		.table-bordered {
			border: none;
		}
	}
</style>

{% var start_date = filters.from_date %}
{% var end_date = filters.to_date %}
{% var tin = data[0][ __("tax_id")] %}

<TABLE cellspacing=0 cellpadding=0" class="table table-bordered">
	<colgroup>
		<col style="width: 2%" /> <!-- SI -->
		<col style="width: 16%" /> <!-- NAME OF EMPLOYEE -->
		<col style="width: 11%" /> <!-- PAYROLL NUMBER -->
		<col style="width: 8%" /> <!-- POSTAL ADDRESS -->
		<col style="width: 7%" /> <!-- POSTAL CITY -->
		<col style="width: 8%" /> <!-- BASIC PAY -->
		<col style="width: 8%" /> <!-- HOUSING -->
		<col style="width: 8%" /> <!-- ALLOWANCES AND BENEFIT -->
		<col style="width: 8%" /> <!-- GROSS PAY -->
		<col style="width: 8%" /> <!-- DEDUCTION (NSSF)-->
		<col style="width: 8%" /> <!-- TAXABLE AMOUNT -->
		<col style="width: 8%" /> <!-- TAX DUE -->
	</colgroup>
	<THEAD>
		<tr>
			<th colspan=12 class="no-line" style="background-color: #ffffff !important; text-align: center;">
				<font style="font-size: 13pt; font-weight: bold;">
					P.A.Y.E. - DETAILS OF PAYMENT OF TAX WITHHELD<br><br>
				</font>
				<table cellspacing=0 cellpadding=0>
					<colgroup>
						<col style="width: 60%" />
						<col style="width: 60%" />
					</colgroup>
					<tbody>
						<tr>
							<td style="border: 1px solid black; border:none !important;"">
							<font style=" font-size: 13pt; font-weight: bold;">Name of Employer: </font>
								<font style="font-size: 13pt; font-weight: normal; text-decoration: underline;">
									{%= data[0][ __("company")] %}
								</font>
							</td>
							<td valign=middle style="vertical-align: top; border:none !important;">
								<table>
									<colgroup>
										<col width="50">
										<col width="30">
										<col width="30">
										<col width="30">
										<col width="30">
										<col width="30">
										<col width="30">
										<col width="30">
										<col width="30">
										<col width="30">
										<col width="30">
										<col width="30">
									</colgroup>
									<tbody>
										<tr>
											<td width=30 style="border:none !important;"><strong>TIN</strong></td>
											<td style="border: 1px solid black; text-align: center;">{%= tin[0] %}</td>
											<td style="border: 1px solid black; text-align: center;">{%= tin[1] %}</td>
											<td style="border: 1px solid black; text-align: center;">{%= tin[2] %}</td>
											<td
												style="border: 1px solid black; text-align: center; background-color: #000;">
											</td>
											<td style="border: 1px solid black; text-align: center;">{%= tin[4] %}</td>
											<td style="border: 1px solid black; text-align: center;">{%= tin[5] %}</td>
											<td style="border: 1px solid black; text-align: center;">{%= tin[6] %}</td>
											<td
												style="border: 1px solid black; text-align: center; background-color: #000;">
											</td>
											<td style="border: 1px solid black; text-align: center;">{%= tin[8] %}</td>
											<td style="border: 1px solid black; text-align: center;">{%= tin[9] %}</td>
											<td style="border: 1px solid black; text-align: center;">{%= tin[10] %}</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</tbody>
				</table>
				<br>
			</th>
		</tr>
		<TR>
			<TH style="text-align: center; color: black; font-weight: bold;">SI</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">NAME OF EMPLOYEE</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">PAYROLL<br>NUMBER</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">POSTAL ADDRESS</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">POSTAL CITY</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">BASIC PAY</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">HOUSING</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">ALLOWANCES AND BENEFIT</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">GROSS PAY</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">DEDUCTION (NSSF)</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">TAXABLE AMOUNT</TH>
			<TH style="text-align: center; color: black; font-weight: bold;">TAX DUE</TH>
		</TR>
	</THEAD>
	<TBODY>
		{%
		var basic_total=0;
		var housing_total=0;
		var other_total=0;
		var gross_total=0;
		var nssf_total=0;
		var tax_amount_total=0;
		var paye_total=0;
		%}
		{% for(var i=0, l=data.length; i<=l-2; i++) { %} <TR>
			{%
			var taxable_amount = data[i][ __("gross")] - data[i][ __("nssf")];
			var basic = data[i][ __("basic")]|| 0;
			var housing = data[i][ __("housing")]|| 0;
			var other = data[i][ __("other")]|| 0;
			var gross = data[i][ __("gross")]|| 0;
			var paye = data[i][ __("paye")]|| 0;
			var nssf = data[i][ __("nssf")]|| 0;

			var basic_total = basic_total + basic;
			var housing_total= housing_total + housing;
			var other_total = other_total + other;
			var gross_total = gross_total + gross;
			var nssf_total = nssf_total + nssf;
			var tax_amount_total = tax_amount_total + taxable_amount;
			var paye_total = paye_total + paye;
			%}
			<TD style="text-align: left; ">{%= i+1 %}</TD>
			<TD style="text-align: left; ">{%= data[i][ __("employee_name")] %}</TD>
			<TD style="text-align: left; ">{%= data[i][ __("employee")] %}</TD>
			<TD style="text-align: left; ">&nbsp;</TD>
			<TD style="text-align: left; ">&nbsp;</TD>
			<TD style="text-align: right;">
				{%= basic.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TD>
			<TD style="text-align: right;">
				{%= housing.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TD>
			<TD style="text-align: right;">
				{%= other.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TD>
			<TD style="text-align: right;">
				{%= gross.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TD>
			<TD style="text-align: right;">
				{%= nssf.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TD>
			<TD style="text-align: right;">
				{%= taxable_amount.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TD>
			<TD style="text-align: right;">
				{%= paye.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TD>
			</TR>
			{% } %}
	</TBODY>
	<TFOOT>
		<TR>
			<TH style="text-align: left; color: black; font-weight: bold;">&nbsp;</TH>
			<TH style="text-align: right; color: black; font-weight: bold;" colspan="4">TOTAL</TH>
			<TH style="text-align: right; color: black; font-weight: bold;">
				{%= basic_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TH>
			<TH style="text-align: right; color: black; font-weight: bold;">
				{%= housing_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TH>
			<TH style="text-align: right; color: black; font-weight: bold;">
				{%= other_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TH>
			<TH style="text-align: right; color: black; font-weight: bold;">
				{%= gross_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TH>
			<TH style="text-align: right; color: black; font-weight: bold;">
				{%= nssf_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TH>
			<TH style="text-align: right; color: black; font-weight: bold;">
				{%= tax_amount_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TH>
			<TH style="text-align: right; color: black; font-weight: bold;">
				{%= paye_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
			</TH>
		</TR>
	</TFOOT>
</TABLE>
