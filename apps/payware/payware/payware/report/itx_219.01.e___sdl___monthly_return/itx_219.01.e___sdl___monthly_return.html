<style>
.print-format {
        padding-left: 10mm;
        padding-right: 5mm;
        padding-top: 5mm;
		font-size: 11pt !important;	
}

.print-format td, .print-format th {
   vertical-align: top !important;
   padding: 2px !important;

}

.small
{
  font-size: 7px;
  text-align: center;
  border-bottom: 2px solid black;
  color: black;
}

.box {
  width: 150px;
  border: 2px solid black;
  padding: 3px;
  margin: 20px;
  text-align: center;
  display:inline-block;
}

.sqbox {
  width: 40px;
  border: 2px solid black;
  padding: 3px;
  margin: 20px;
  text-align: center;
  display:inline-block;
}

@media screen {
	.print-format {
		width: 8.5in;
		font-size: 11pt !important;	
	}
	.print-format td, .print-format th {
		vertical-align: top !important;
		padding: 2px !important;
	}
	.small{
		font-size: 7px;
		text-align: center;
		border-bottom: 2px solid black;
		color: black;
	}
	
	.box {
		width: 150px;
		border: 2px solid black;
		padding: 3px;
		margin: 20px;
		text-align: center;
		display:inline-block;
	}
	
	.sqbox {
		width: 40px;
		border: 2px solid black;
		padding: 3px;
		margin: 20px;
		text-align: center;
		display:inline-block;
	}
}
</style>

{% var start_date = filters.from_date  %}
{% var end_date = filters.to_date  %}
{% var tin = data[0][ __("TIN")]  %}
{% var sdl_rate = data[0][ __("SDL Rate")] %}



{%
	
	var subtotalA = data[0][ __("Basic pay")] + data[0][ __("Leave pay")] + data[0][ __("Sick pay")] +
					data[0][ __("Payment in Lieu of Leave")] + data[0][ __("Fees")] + 
					data[0][ __("Commission")] + data[0][ __("Bonus")];
					
	var subtotalB = data[0][ __("Gratuity")] + data[0][ __("Subsistence Allowance")] + data[0][ __("Traveling Allowance")] +
					data[0][ __("Entertainment Allowance")] + data[0][ __("Housing Allowance")] + 
					data[0][ __("Any Other Allowances")];
	
	var grand_total = subtotalA + subtotalB;

%}
<p class="manifest-header text-center">
   <img src="/files/tra-logo.gif" style="width: 35%;"/>
</p>
<h3 class="manifest-header text-center">
	SKILLS AND DEVELOPMENT LEVY<br>
	MONTHLY RETURN 

</h3>

	<table cellspacing=0 cellpadding=0 style="border: 0px solid black;" width=100%>
	  <colgroup>
		<col style="width: 50%" />
		<col style="width: 50%" />
	  </colgroup>
	  <tbody>
		<tr>
		  <td></td>
		  <td>
			<table>
			  <colgroup>
				<col width="70">
				<col width="35">
				<col width="35">
				<col width="35">
				<col width="35">
			  </colgroup>
			  <tbody >
				<tr>
				  <td width=30>YEAR</td>
				  <td style="border: 1px solid black; text-align: center;">{%= start_date[0] %}</td>
				  <td style="border: 1px solid black; text-align: center;">{%= start_date[1] %}</td>
				  <td style="border: 1px solid black; text-align: center;">{%= start_date[2] %}</td>
				  <td style="border: 1px solid black; text-align: center;">{%= start_date[3] %}</td>
				</tr>
			  </tbody>
			</table>
		  </td>
		</tr>
		<tr style="line-height: 4px">
			<td colspan=2>&nbsp;</td>
		</tr>
		<tr style="line-height: 25px">
			<td colspan=2 style="border: 1px solid black;">
				<div style="font-weight: bold;">TO:</div>
				<div style=" margin: auto; width: 60%; font-weight: bold;">COMMISIONER FOR DOMESTIC REVENUE</div>
			</td>
		</tr>
		<tr style="line-height: 10px">
			<td colspan=2>&nbsp;</td>
		</tr>
		<tr>
			<td colspan=2>
				<font style="font-weight: bold;">EMPLOYER’S INFORMATION</font>
				<table>
					<colgroup>
						<col width="70">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
					</colgroup>
					<tbody>
						<tr>
							<td width=30>TIN</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[0] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[1] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[2] %}</td>
							<td style="border: 1px solid black; text-align: center; background-color: #000;"></td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[4] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[5] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[6] %}</td>
							<td style="border: 1px solid black; text-align: center; background-color: #000;"></td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[8] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[9] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[10] %}</td>
						</tr>
				</tbody>
				</table>
				<table style="border-spacing: 15px 30px;" border=0>
					<colgroup>
						<col width="200">
						<col width="200">
						<col width="200">
						<col width="200">
					</colgroup>
					<tbody >
						<tr>
							<td colspan=4><font style="font-weight: bold;">Name of Employer:</td>
						</tr>
						<tr>
							<td colspan=4>
							<table border=1>
								<colgroup>
									<col width="700">
								</colgroup>
								<tbody>
								<tr>
									<td style="border: 1px solid black; text-align: left;">
										{%= data[0][ __("Company")] %}
									</td>
								</tr>
							</tbody>
							</table>
							</td>
						</tr>
						<tr style="line-height: 4px">
							<td colspan=4>&nbsp;</td>
						</tr>						<tr>
							<td colspan=4><font style="font-weight: bold;">Postal Address:</font></td>
						</tr>
						<tr>
							<td><font style="">P. O. Box</font></td>
							<td style="border: 1px solid black; text-align: center;">{%= data[0][ __("P O Box")] %}</td>
							<td><font style="">Postal City</font></td>
							<td style="border: 1px solid black; text-align: center;">{%= data[0][ __("City")] %}</td>
						</tr>
						<tr style="line-height: 4px">
							<td colspan=4>&nbsp;</td>
						</tr>
						<tr>
							<td colspan=4><font style="font-weight: bold;">Physical Adddress:</font></td>
						</tr>
						<tr>
							<td><font style="">Plot Number</font></td>
							<td style="border: 1px solid black; text-align: center;">{%= data[0][ __("Plot Number")] %}</td>
							<td><font style="">Block Number </font></td>
							<td style="border: 1px solid black; text-align: center;">{%= data[0][ __("Block Number")] %}</td>
						</tr>
						<tr style="line-height: 4px">
							<td colspan=4>&nbsp;</td>
						</tr>
						<tr>
							<td><font style="font-weight: bold;">Street/Location</font></td>
							<td style="border: 1px solid black; text-align: left;" colspan=3>{%= data[0][ __("Street")] %}</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr>
			<td colspan=2>
				<hr style="border-top: solid 1px;" />
				I forward herewith SDL Return for the month of <u>&nbsp;&nbsp;&nbsp;{%= data[0][ __("Month")] %}&nbsp;&nbsp;&nbsp;</u>
				<table class="table table-bordered">
					<colgroup>
						<col width="30%">
						<col width="20%">
						<col width="30%">
						<col width="20%">
					</colgroup>
					<!--
						Basic pay
						Leave pay
						Sick pay
						Payment in Lieu of Leave
						Fees
						Commission
						Bonus
						Gratuity
						Subsistence Allowance
						Traveling Allowance
						Entertainment Allowance
						Housing Allowance
						Any Other Allowances
					-->
					<tr>
						<th>EMOLUMENTS</th>
						<th>AMOUNT / TZS</th>
						<th>EMOLUMENTS</th>
						<th>AMOUNT / TZS</th>
					</tr>
					<tr>
						<td>Basic pay</td>
						<td style="text-align: right;">{%= data[0][ __("Basic pay")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
						<td>Gratuity</td>
						<td style="text-align: right;">{%= data[0][ __("Gratuity")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
					</tr>
					<tr>
						<td>Leave pay</td>
						<td style="text-align: right;">{%= data[0][ __("Leave pay")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
						<td>Subsistence Allowance *)</td>
						<td style="text-align: right;">{%= data[0][ __("Subsistence Allowance")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
					</tr>
					<tr>
						<td>Sick pay</td>
						<td style="text-align: right;">{%= data[0][ __("Sick pay")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
						<td>Traveling Allowance *)</td>
						<td style="text-align: right;">{%= data[0][ __("Traveling Allowance")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
					</tr>
					<tr>
						<td>Payment in Lieu of leave</td>
						<td style="text-align: right;">{%= data[0][ __("Gratuity")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
						<td>Entertainment Allowance *)</td>
						<td style="text-align: right;">{%= data[0][ __("Gratuity")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
					</tr>
					<tr>
						<td>Fees</td>
						<td style="text-align: right;">{%= data[0][ __("Fees")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
						<td>Any other Allowance *)</td>
						<td style="text-align: right;">{%= data[0][ __("Any Other Allowances")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
					</tr>
					<tr>
						<td>Commission</td>
						<td style="text-align: right;">{%= data[0][ __("Gratuity")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
						<td>Housing Allowance</td>
						<td style="text-align: right;">{%= data[0][ __("Housing Allowance")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
					</tr>
					<tr>
						<td>Bonus</td>
						<td style="text-align: right;">{%= data[0][ __("Bonus")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
						<td>Subtotal B</td>
						<td style="text-align: right;">{%= subtotalB.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
					</tr>
					<tr>
						<td>Subtotal A</td>
						<td style="text-align: right;">{%= subtotalA.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
						<td><b>Grand Total (A+B)</b></td>
						<td style="text-align: right;">{%= grand_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
					</tr>
					<tr>
						<th></th>
						<th></th>
						<th>Whereof SDL at {%= sdl_rate %}%<br>amounts to</th>
						<th  style="text-align: right;">{%= data[0][ __("SDL")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</th>
					</tr>
				</table>
				Payment made at the Bank Branch ……………………………………………………………………..
				<br><br>
				Through Payment Slip/Deposit Slip …………………… dated………………………………………...
				<br><br>
				Signature: ………………………………………………….. Date: ………………………………………
				<br>
				Rubber Stamp:

			</td>
		</tr>
	  </tbody>
	</table>
