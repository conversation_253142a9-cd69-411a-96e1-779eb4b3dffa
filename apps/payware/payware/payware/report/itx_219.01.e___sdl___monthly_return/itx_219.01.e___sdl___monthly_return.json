{"add_total_row": 0, "creation": "2020-02-17 17:35:48.916111", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2020-02-22 09:28:22.326557", "modified_by": "Administrator", "module": "Payware", "name": "ITX 219.01.E - SDL - Monthly Return", "owner": "Administrator", "prepared_report": 0, "query": "SELECT sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Basic pay', sd.amount, 0)) as \"Basic pay:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Leave pay', sd.amount, 0)) as \"Leave pay:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Sick pay', sd.amount, 0)) as \"Sick pay:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Payment in Lieu of Leave', sd.amount, 0)) as \"Payment in Lieu of Leave:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Fees', sd.amount, 0)) as \"Fees:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Commission', sd.amount, 0)) as \"Commission:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Bonus', sd.amount, 0)) as \"Bonus:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Gratuity', sd.amount, 0)) as \"Gratuity:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Subsistence Allowance', sd.amount, 0)) as \"Subsistence Allowance:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Traveling Allowance', sd.amount, 0)) as \"Traveling Allowance:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Entertainment Allowance', sd.amount, 0)) as \"Entertainment Allowance:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Housing Allowance', sd.amount, 0)) as \"Housing Allowance:Currency:\",\r\n       sum(if(sd.parentfield = 'earnings' and sd.do_not_include_in_total = 0 and sc.sdl_emolument_category = 'Any Other Allowances', sd.amount, 0)) as \"Any Other Allowances:Currency:\",\r\n       sum(if(sd.salary_component = 'SDL', sd.amount, 0)) as \"SDL:Currency:\",\r\n       ss.company as \"Company:Data:0\",\r\n       cmp.tax_id as \"TIN:Data:0\",\r\n       cmp.email as \"Email:Data:0\",\r\n       cmp.phone_no as \"Phone:Data:0\",\r\n       cmp.fax as \"Fax Number:Data:0\",\r\n       pwspob.value as \"P O Box:Data:0\",\r\n       pwsst.value as \"Street:Data:0\",\r\n       pwspn.value as \"Plot Number:Data:0\",\r\n       pwsbn.value as \"Block Number:Data:0\",\r\n       pwscty.value as \"City:Data:0\",\r\n       pwssdl.value as \"SDL Rate:Data:0\",\r\n       DATE_FORMAT(ss.start_date, '%%M %%Y') as \"Month:Data:0\"\r\nFROM `tabSalary Slip` ss LEFT OUTER JOIN `tabSalary Detail` sd ON sd.parent = ss.name\r\n                         LEFT OUTER JOIN `tabCompany` cmp ON ss.company = cmp.name \r\n                         LEFT OUTER JOIN `tabSalary Component` sc ON sd.salary_component = sc.name and sc.type = 'Earning'\r\n                         JOIN `tabSingles` pwspob ON pwspob.doctype = \"Payware Settings\" and pwspob.field = \"p_o_box\"\r\n                         JOIN `tabSingles` pwsst ON pwsst.doctype = \"Payware Settings\" and pwsst.field = \"street\"\r\n                         JOIN `tabSingles` pwspn ON pwspn.doctype = \"Payware Settings\" and pwspn.field = \"plot_number\"\r\n                         JOIN `tabSingles` pwsbn ON pwsbn.doctype = \"Payware Settings\" and pwsbn.field = \"block_number\"\r\n                         JOIN `tabSingles` pwscty ON pwscty.doctype = \"Payware Settings\" and pwscty.field = \"city\"\r\n                         JOIN `tabSingles` pwssdl ON pwssdl.doctype = \"Payware Settings\" and pwssdl.field = \"skills_development_levy\"\r\nWHERE ss.start_date = %(from_date)s\r\nAND ss.end_date = %(to_date)s\r\nAND ss.docstatus = 1", "ref_doctype": "<PERSON><PERSON>", "report_name": "ITX 219.01.E - SDL - Monthly Return", "report_type": "Query Report", "roles": [{"role": "HR Manager"}, {"role": "HR User"}, {"role": "Employee"}, {"role": "System Manager"}, {"role": "Accounts User"}, {"role": "Accounts Manager"}]}