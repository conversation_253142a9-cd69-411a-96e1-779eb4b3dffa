<style>
	.print-format {
		padding-left: 10mm;
		padding-right: 5mm;
		padding-top: 5mm;
		font-size: 13pt;
	}

	.print-format td,
	.print-format th {
		vertical-align: top !important;
		padding: 2px !important;
		font-size: 14px;
	}

	.print-format th {
		color: black;
		font-weight: bold;
		background: lightgrey;
	}

	.small {
		font-size: 7px;
		text-align: center;
		border-bottom: 2px solid black;
		color: black;
	}

	.box {
		width: 150px;
		border: 2px solid black;
		padding: 3px;
		margin: 20px;
		text-align: center;
		display: inline-block;
	}

	.sqbox {
		width: 40px;
		border: 2px solid black;
		padding: 3px;
		margin: 20px;
		text-align: center;
		display: inline-block;
	}

	@media screen {
		.print-format {
			width: 8.5in;
		}

		.print-format td,
		.print-format th {
			vertical-align: top !important;
			padding: 2px !important;
		}

		.small {
			font-size: 7px;
			text-align: center;
			border-bottom: 2px solid black;
			color: black;
		}

		.box {
			width: 150px;
			border: 2px solid black;
			padding: 3px;
			margin: 20px;
			text-align: center;
			display: inline-block;
		}

		.sqbox {
			width: 40px;
			border: 2px solid black;
			padding: 3px;
			margin: 20px;
			text-align: center;
			display: inline-block;
		}
	}
</style>

{%
var start_date = filters.from_date;
var end_date = filters.to_date;
var tin = data[0][ __("tin")];
%}

{%
if ( end_date[5]+ end_date[6] == "06") {
var tick1 = "X";
}

if ( end_date[5]+ end_date[6] == "12") {
var tick2 = "X";
}
var sdl_rate = data[0][ __("sdl_rate")]
%}


<p class="manifest-header text-center">
	<img src="/files/tra-logo.gif" style="width: 35%;" />
</p>
<h3 class="manifest-header text-center">
	SKILLS AND DEVELOPMENT LEVY<br>
	EMPLOYER’S HALF YEAR CERTIFICATE
</h3>
<br>
<table cellspacing=0 cellpadding=0 style="border: 0px solid black;" width=100%>
	<colgroup>
		<col style="width: 50%" />
		<col style="width: 50%" />
	</colgroup>
	<tbody>
		<tr>
			<td></td>
			<td>
				<table>
					<colgroup>
						<col width="70">
						<col width="35">
						<col width="35">
						<col width="35">
						<col width="35">
					</colgroup>
					<tbody>
						<tr>
							<td width=30>YEAR</td>
							<td style="border: 1px solid black; text-align: center;">{%= start_date[0] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= start_date[1] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= start_date[2] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= start_date[3] %}</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr>
			<td colspan=2>
				<div class="small">
					<p>
						(To be submitted to the TRA office within 30 days after the end of each six-month calendar
						period)
					</p>
				</div>
				<h6 class="manifest-header text-left">EMPLOYER’S INFORMATION</h6>
				<table>
					<colgroup>
						<col width="70">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
						<col width="30">
					</colgroup>
					<tbody>
						<tr>
							<td width=30>TIN</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[0] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[1] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[2] %}</td>
							<td style="border: 1px solid black; text-align: center; background-color: #000;"></td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[4] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[5] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[6] %}</td>
							<td style="border: 1px solid black; text-align: center; background-color: #000;"></td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[8] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[9] %}</td>
							<td style="border: 1px solid black; text-align: center;">{%= tin[10] %}</td>
						</tr>
					</tbody>
				</table>
				<table style="border-spacing: 15px 30px;" border="0">
					<colgroup>
						<col width="200">
						<col width="200">
						<col width="200">
						<col width="200">
					</colgroup>
					<tbody>
						<tr style="line-height: 5px">
							<td colspan=4>&nbsp;</td>
						</tr>
						<tr>
							<td colspan=4>
								<font style="font-weight: bold;">Name of Employer:</font>
							</td>
						</tr>
						<tr>
							<td colspan=4>
								<table border=1>
									<colgroup>
										<col width="700">
									</colgroup>
									<tbody>
										<tr>
											<td style="border: 1px solid black; text-align: left;">
												{%= data[0][ __("company")] %}
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr style="line-height: 5px">
							<td colspan=4>&nbsp;</td>
						</tr>
						<tr>
							<td colspan=4>
								<font style="font-weight: bold;">Postal Address:</font>
							</td>
						</tr>
						<tr>
							<td>
								<font style="">P. O. Box</font>
							</td>
							<td style="border: 1px solid black; text-align: center;">{%= data[0][ __("pobox")] %}</td>
							<td>
								<font style="">Postal City</font>
							</td>
							<td style="border: 1px solid black; text-align: center;">{%= data[0][ __("city")] %}</td>
						</tr>
						<tr style="line-height: 5px">
							<td colspan=4>&nbsp;</td>
						</tr>
						<tr>
							<td colspan=4>
								<font style="font-weight: bold;">Physical Adddress:</font>
							</td>
						</tr>
						<tr>
							<td>
								<font style="">Plot Number</font>
							</td>
							<td style="border: 1px solid black; text-align: center;">
								{%= data[0][ __("plot_no")] %}
							</td>
							<td>
								<font style="">Block Number </font>
							</td>
							<td style="border: 1px solid black; text-align: center;">
								{%= data[0][ __("block_no")] %}
							</td>
						</tr>
						<tr style="line-height: 5px">
							<td colspan=4>&nbsp;</td>
						</tr>
						<tr>
							<td>
								<font style="font-weight: bold;">Street/Location</font>
							</td>
							<td style="border: 1px solid black; text-align: left;" colspan=3>{%= data[0][ __("street")]
								%}</td>
						</tr>
						<tr style="line-height: 5px">
							<td colspan=4>&nbsp;</td>
						</tr>
						<tr>
							<td>
								<font style="font-weight: bold;">Nature of business</font>
							</td>
							<td style="border: 1px solid black; text-align: left;" colspan=3></td>
						</tr>
						<tr style="line-height: 5px">
							<td colspan=4>&nbsp;</td>
						</tr>
						<tr>
							<td colspan=2>
								<font style="font-weight: bold;">State whether an Entity or Individual:</font>
							</td>
							<td style="border: 1px solid black; text-align: left;" colspan=2></td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr>
			<td colspan=2>
				<font style=" font-weight: bold;">
					<br>SUMMARY OF GROSS EMOLUMENTS AND TAX PAID DURING THE YEAR
				</font>
				<table class="table table-bordered" style="font-size: 11px;" width=90% border=1>
					<colgroup>
						<col style="width: 20%" />
						<col style="width: 20%" />
						<col style="width: 20%" />
						<col style="width: 20%" />
						<col style="width: 20%" />
					</colgroup>
					<thead>
						<th style="text-align: center;">Month</th>
						<th style="text-align: center;">Payment to<br>permanent<br>employees/TZS</th>
						<th style="text-align: center;">Payment to<br>casual<br>employees/TZS</th>
						<th style="text-align: center;">Total gross<br>emoluments<br>TZS</th>
						<th style="text-align: center;">Amount of<br>SDL paid<br>TZS</th>
					</thead>
					<tbody>
					{% for(var i=0, l=data.length; i<l; i++) { 
						if (i+1 !=l) {
					%} 
							<tr>
								<TD style="text-align: left; ">{%= data[i][ __("month")] %}</TD>
								<TD style="text-align: right;">
									{%= data[i][ __("gross")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
								</TD>
								<TD style="text-align: right;"></TD>
								<TD style="text-align: right;">
									{%= data[i][ __("gross")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
								</TD>
								<TD style="text-align: right;">
									{%= data[i][ __("sdl")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
								</TD>
							</tr>
					{% 
						} 
						else { 
							var tgp = data[i][ __("gross")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
							var tsdl = data[i][ __("sdl")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
					%}
								<TR>
									<TH style="text-align: left; ">TOTAL</TH>
									<TH style="text-align: right;">
										{%= data[i][ __("gross")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
									</TH>
									<TH style="text-align: right;"></TH>
									<TH style="text-align: right;">
											{%= data[i][ __("gross")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
										</TH>
									<TH style="text-align: right;">
										{%= data[i][ __("sdl")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
									</TH>
								</TR>
					{%	
						}
					}
					%}
					</tbody>
				</table>
			</td>
		</tr>
	</tbody>
</table>
<div class="page-break"></div>
<table cellspacing=0 cellpadding=0 style="border: 0px solid black;" width=100%>
    <colgroup>
        <col style="width: 50%" />
        <col style="width: 50%" />
    </colgroup>
    <tbody>
        <tr style="line-height: 50px">
            <td colspan=2>&nbsp;</td>
        </tr>
        <tr>
            <td colspan=2>
                The amount of gross emoluments paid during the period from (please tick the appropriate box)
            </td>
        </tr>
        <tr>
            <td>
                <table style="border: 0px solid black !important;">
                    <colgroup>
                        <col width="35">
                        <col width="400">
                    </colgroup>
                    <tbody>
                        <tr>
                            <td style="border: 1px solid black; text-align: center;">{%= tick1 %}</td>
                            <td style="border: 0px solid black !important;">
                                <font style="font-weight: bold;">From 1 January to 30 June</font>
                            </td>
                        </tr>
                        <tr>
                            <td style="border: 0px solid black !important; line-height: 10px;">&nbsp;</td>
                            <td style="border: 0px solid black !important; line-height: 10px;">&nbsp;</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid black; text-align: center;">{%= tick2 %}</td>
                            <td style="border: 0px solid black !important;">
                                <font style="font-weight: bold;">From 1 January to 31 December</font>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td></td>
        </tr>
        <tr style="line-height: 5px">
            <td colspan=2>&nbsp;</td>
        </tr>
        <tr>
            <td colspan=2>
                added up to TZS
                <div class="box">
                    {%= tgp %}
                </div>
                and {%= sdl_rate %}% thereof is
                <div class="box">
                    {%= tsdl %}
                </div>
            </td>
        </tr>
        <tr style="line-height: 25px">
            <td colspan=2>&nbsp;</td>
        </tr>
        <tr>
            <td colspan=2>
                <strong>DECLARATION</strong>
                <BR><Br>
                I certify that the particulars entered on the form SDL already submitted monthly for the period
                indicated above are correct.
                <BR><Br>
                Name of the Employer/Paying Officer
                <hr style="border-top: dotted 1px;" />
                Title: Mr.<div class="sqbox">&nbsp;</div> Mrs<div class="sqbox">&nbsp;</div>Ms<div class="sqbox">&nbsp;
                </div>
                <table style="width: 75%;">
                    <colgroup>
                        <col width="250">
                        <col width="20">
                        <col width="250">
                        <col width="20">
                        <col width="250">
                    </colgroup>
                    <tr>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 0px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 0px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="border: 0px solid black; text-align: center;">First Name</td>
                        <td style="border: 0px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 0px solid black; text-align: center;">Middle Name</td>
                        <td style="border: 0px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 0px solid black; text-align: center;">SurName</td>
                    </tr>
                </table>
                <br>
                Signature and rubber stamp of the Employer/Paying Officer
                <br><br><br>
                ..........................................................................
                <br><br>
                <table style="width: 50%;">
                    <colgroup>
                        <col width="70">
                        <col width="30">
                        <col width="30">
                        <col width="30">
                        <col width="30">
                        <col width="30">
                        <col width="30">
                        <col width="30">
                        <col width="30">
                        <col width="30">
                        <col width="30">
                    </colgroup>
                    <tr>
                        <td style="border: 0px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 0px solid black; text-align: center;" colspan=2>Day</td>
                        <td style="border: 0px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 0px solid black; text-align: center;" colspan=2>Month</td>
                        <td style="border: 0px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 0px solid black; text-align: center;" colspan=4>Year</td>
                    </tr>
                    <tr>
                        <td style="border: 0px solid black; text-align: center;">Date</td>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 0px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 0px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                        <td style="border: 1px solid black; text-align: center;">&nbsp;</td>
                    </tr>
                </table>
            </td>
        </tr>
	</tbody>
</table>

