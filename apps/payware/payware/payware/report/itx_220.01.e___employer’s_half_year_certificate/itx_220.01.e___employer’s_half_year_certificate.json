{"add_total_row": 1, "columns": [], "creation": "2020-03-04 10:07:03.999150", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [], "idx": 0, "is_standard": "Yes", "modified": "2021-07-27 18:14:15.898658", "modified_by": "Administrator", "module": "Payware", "name": "ITX 220.01.E - Employer’s Half Year Certificate", "owner": "Administrator", "prepared_report": 0, "query": "SELECT\tdate_format(ss.start_date, \"%%M\") as month,\n\tSUM(IF (sd.salary_component = 'Basic', ss.gross_pay, 0)) AS gross,\n\tSUM(IF (sd.salary_component = 'SDL', sd.amount, 0)) AS sdl,\n\tss.company AS company,\n\tcmp.tax_id AS tin,\n\tpwssdl.value AS sdl_rate,\n    pwspob.value as pobox,\n    pwsst.value as street,\n    pwspn.value AS plot_no,\n    pwsbn.value AS block_no,\n    pwscty.value AS city\nFROM `tabSalary Slip` ss \n            LEFT OUTER JOIN `tabSalary Detail` sd ON ss.name = sd.parent\n            LEFT OUTER JOIN `tabCompany` cmp ON ss.company = cmp.name \n\t\t    JOIN `tabSingles` pwssdl ON pwssdl.doctype = \"Payware Settings\" AND pwssdl.field = 'skills_development_levy'\n            JOIN `tabSingles` pwspob ON pwspob.doctype = \"Payware Settings\" AND pwspob.field = \"p_o_box\" \n            JOIN `tabSingles` pwsst ON pwsst.doctype = \"Payware Settings\" AND pwsst.field = \"street\" \n            JOIN `tabSingles` pwspn ON pwspn.doctype = \"Payware Settings\" AND pwspn.field = \"plot_number\" \n            JOIN `tabSingles` pwsbn ON pwsbn.doctype = \"Payware Settings\" AND pwsbn.field = \"block_number\" \n            JOIN `tabSingles` pwscty ON  pwscty.doctype = \"Payware Settings\" AND pwscty.field = \"city\" \nWHERE\tss.start_date >= %(from_date)s\nAND\tss.end_date <= %(to_date)s\nAND     ss.docstatus = 1\nGROUP BY date_format(ss.start_date, \"%%M\")\nORDER BY ss.start_date\n", "ref_doctype": "<PERSON><PERSON>", "report_name": "ITX 220.01.E - Employer’s Half Year Certificate", "report_type": "Query Report", "roles": [{"role": "HR Manager"}, {"role": "HR User"}, {"role": "Employee"}, {"role": "System Manager"}]}