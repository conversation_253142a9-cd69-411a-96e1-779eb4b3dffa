{"add_total_row": 0, "creation": "2020-01-14 06:37:53.848184", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2020-01-24 18:32:53.691529", "modified_by": "Administrator", "module": "Payware", "name": "ITX 300.01.E - Employment Taxes Payment Credit Slip", "owner": "Administrator", "prepared_report": 0, "query": "SELECT  sum(if(sd.salary_component = 'SDL', sd.amount, 0)) as \"SDL:Currency:\",\r\n\t\tsum(if(sd.salary_component = 'PAYE', sd.amount, 0)) as \"PAYE:Currency:\",\r\n\t\tss.company as \"Company:Data:0\",\r\n\t\tcmp.tax_id as \"TIN:Data:0\",\r\n\t\tcmp.email as \"Email:Data:0\",\r\n\t\tcmp.phone_no as \"Phone:Data:0\",\r\n\t\tcmp.fax as \"Fax Number:Data:0\",\r\n\t\tpwspob.value as \"P O Box:Data:0\",\r\n\t\tpwsst.value as \"Street:Data:0\",\r\n\t\tpwspn.value as \"Plot Number:Data:0\",\r\n\t\tpwsbn.value as \"Block Number:Data:0\",\r\n\t\tpwscty.value as \"City:Data:0\",\r\n\t\tpwssdl.value as \"SDL Rate:Data:0\",\r\n\t\tDATE_FORMAT(ss.start_date, '%%M %%Y') as \"Month:Data:0\"\r\nFROM `tabSalary Slip` ss    LEFT OUTER JOIN `tabSalary Detail` sd ON sd.parent = ss.name \r\n\t\t\t\t\t\t\tLEFT OUTER JOIN `tabCompany` cmp  ON ss.company = cmp.name \r\n\t\t\t\t\t\t\tLEFT OUTER JOIN `tabSalary Component` sc ON sd.salary_component = sc.name\r\n\t\t\t\t\t\t\tJOIN `tabSingles` pwspob   ON pwspob.doctype = \"Payware Settings\" and pwspob.field = \"p_o_box\"\r\n\t\t\t\t\t\t\tJOIN `tabSingles` pwsst   ON pwsst.doctype = \"Payware Settings\" and pwsst.field = \"street\"\r\n\t\t\t\t\t\t\tJOIN `tabSingles` pwspn  ON pwspn.doctype = \"Payware Settings\" and pwspn.field = \"plot_number\"\r\n\t\t\t\t\t\t\tJOIN `tabSingles` pwsbn   ON pwsbn.doctype = \"Payware Settings\" and pwsbn.field = \"block_number\"\r\n\t\t\t\t\t\t\tJOIN `tabSingles` pwscty  ON pwscty.doctype = \"Payware Settings\" and pwscty.field = \"city\"\r\n\t\t\t\t\t\t\tJOIN `tabSingles` pwssdl  ON pwssdl.doctype = \"Payware Settings\" and pwssdl.field = \"skills_development_levy\"\r\nWHERE ss.start_date = %(from_date)s\r\nAND ss.end_date = %(to_date)s\r\nAND ss.docstatus = 1", "ref_doctype": "<PERSON><PERSON>", "report_name": "ITX 300.01.E - Employment Taxes Payment Credit Slip", "report_type": "Query Report", "roles": [{"role": "HR Manager"}, {"role": "HR User"}, {"role": "Employee"}, {"role": "System Manager"}]}