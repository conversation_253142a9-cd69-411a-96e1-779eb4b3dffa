<style>
    .print-format {
            padding-left: 10mm;
            padding-right: 5mm;
            padding-top: 0mm;
            font-size: 9pt;	
    }
    
    .print-format td, .print-format th {
        vertical-align: top !important;
        padding: 1px !important;
    }
    
    .tbody { display: block; page-break-before: avoid; }
    .tbody { display: block; page-break-after: always; }
    
    @media screen {
        .print-format {
            padding-left: 10mm;
            padding-right: 5mm;
            padding-top: 0mm;
            font-size: 9pt;
            width: 8.5in;
            height: 11in;
            }
        
        .print-format td, .print-format th {
        vertical-align: top !important;
        padding: 1px !important;
        }
        
        .tbody { display: block; page-break-before: avoid; }
        .tbody { display: block; page-break-after: always; }
    
    }
    </style>

<!-- {%	var from_date = filters.from_date %}
{%	var year = from_date[0] + from_date[1] + from_date[2] + from_date[3] %}
{%	var pmethod = data[0][ __("Payment Method")] + " (" + data[0][ __("Ref No.")] + ")" %}
{%  var page_no = 1 %}

{% var total_nssf = 0  %}
{% var page_total = 0  %}
{% for(var i=0, l=data.length; i<=l-1; i++) { %} 
	{% var total_nssf = total_nssf + data[i][ __("Contribution")]  %}
{% } %}
-->

{%= data[0][__("Company")] %}

<!-- <table class="table table-bordered">
	<colgroup>
		<col style="width: 3%">
		<col style="width: 28%">
		<col style="width: 23%">
		<col style="width: 12%">
		<col style="width: 10%">
		<col style="width: 12%">
		<col style="width: 12%">
	</colgroup>
	<thead>
		<tr>
			<td colspan=7  style="border: 0px;">
				<p class="manifest-header text-center">
					<img src="/files/nssf conf.5 letter head.png" style="width: 100%;"/>
                </p>
                <table style="border-spacing: 0; border-collapse: collapse;">
					<colgroup style="width: 780px;">
						<col style="width: 510px;">
						<col style="width: 270px;">
					</colgroup>
					<tr>
						<td style="border: 0px;"> 
							<table style="border-spacing: 0; border-collapse: collapse;">
								<colgroup style="width: 510px;">
									<col style="width: 180px;">
									<col style="width: 330px;">
								</colgroup>
								<tr>
									<td style="border: 0px;"><B>Employer Name : </B></td>
									<td style="border: 0px;">{%= data[0][__("Company")] %}</td>
								</tr>
								<tr>
									<td style="border: 0px;"><B>Address : </B></td>
									<td style="border: 0px;">P. O. Box {%= data[0][ __("P O Box")] %}<br>{%= data[0][ __("City")] %}<br>Tanzania</td>
								</tr>
							</table>
							<table style="border-spacing: 0; border-collapse: collapse;">
								<colgroup style="width: 510px;">
									<col style="width: 255px;">
									<col style="width: 255px;">
								</colgroup>
								<tr>
									<td style="border: 0px;"><B>Employer&apos;s Registration Number : </B></td>
									<td style="border: 0px;">
										<div style="border: 1px solid black; padding: 2px; text-align: center; width: 200px; height: 18px;">
											<span>{%= data[0][ __("NSSF Employer No")] %}</span>
										<div>
									</td>
								</tr>
								<tr>
									<td style="border: 0px;"><B>Month of Contribution : </B></td>
									<td style="border: 0px;">
										<div>
											<div style="float: left; width: 100px; border: 1px solid black; padding: 2px;text-align: center;  height: 18px;">
												{%= data[0][ __("Contribution Month")] %}
											</div>
											<div style="float: left; padding: 2px; text-align: center;">
												<b>&nbsp;Year : </b>
											</div>
											<div style="float: left; border: 1px solid black; padding: 2px; text-align: center;  height: 18px;">
												{%= data[0][ __("Contribution Year")] %}
											</div>
										</div>
									</td>
								</tr> 
								<tr>
									<td style="border: 0px;"><B>Regional/District Code No.: </B></td>
									<td style="border: 0px;">&nbsp;</td>
								</tr>
							</table>
						</td>
						<td style="border: 0px;"> 
							<table style="border-spacing: 0; border-collapse: collapse;">
								<colgroup style="width: 270px;">
									<col style="width: 135px;">
									<col style="width: 135px;">
								</colgroup>
								<tr>
									<td style="border: 0px;"><b>Page No:</b></td>
									<td style="border: 0px;">
										<div style="border: 1px solid black; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span>&nbsp;{%= page_no %}</span>
										<div>
									</td>
								</tr>
								<tr>
									<td style="border: 0px;"><b>Cheque No.: </b></td>
									<td style="border: 0px;">
										<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span>&nbsp;</span>
										<div>
									</td>
								</td>
								<tr>
									<td style="border: 0px;"><b>Amount T Shs.</b></td>
									<td style="border: 0px;">
										<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span><b>&nbsp;{%= total_nssf.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</b></span>
										<div>
									</td>
								</td>
								<tr>
									<td style="border: 0px;"><b>Cash T Shs.</b></td>
									<td style="border: 0px;">
										<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span>&nbsp;</span>
										<div>
									</td>
								</td>
								<tr>
									<td style="border: 0px;"><b>Receipt No.: </b></td>
									<td style="border: 0px;">
										<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span>&nbsp;</span>
										<div>
									</td>
								</td>
								<tr>
									<td style="border: 0px;"><b>Date: </b></td>
									<td style="border: 0px;">
										<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span>&nbsp;{%= frappe.datetime.str_to_user(data[0][ __("Payment Date")]) %}</span>
									</td>
								</td>
							</table>
						</td>
					</tr>
				</table>
            </td>
        </tr>
    </thead>
</table> -->