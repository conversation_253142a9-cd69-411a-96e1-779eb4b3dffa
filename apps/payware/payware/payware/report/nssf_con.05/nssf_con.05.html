<style>
    .print-format {
        padding-left: 10mm;
        padding-right: 5mm;
        padding-top: 0mm;
        font-size: 10pt;
    }

    .print-format table {
        width: 100% !important;
        table-layout: fixed;
    }

    .print-format td,
    .print-format th {
        vertical-align: top !important;
        padding: 1px !important;
    }

    @media screen {
        .print-format {
            padding-left: 10mm;
            padding-right: 5mm;
            padding-top: 0mm;
            font-size: 10pt;
            width: 8.5in;
            height: 11in;
        }

        .print-format table {
            width: 100% !important;
            table-layout: fixed;
        }

        .print-format td,
        .print-format th {
            vertical-align: top !important;
            padding: 1px !important;
        }

    }
</style>

{% var from_date = filters.from_date %}
{% var year = from_date[0] + from_date[1] + from_date[2] + from_date[3] %}
{% var pmethod = data[0][ __("Payment Method")] + " (" + data[0][ __("Ref No.")] + ")" %}
{% var page_no = 1 %}

{% var total_nssf = 0 %}
{% var page_total = 0 %}
{% for(var i=0, l=data.length; i<=l-1; i++) { %} {% var total_nssf=total_nssf + data[i][("contribution")] %} {% } %}
    <table style="margin-left: auto; margin-right: auto; border: 0px solid black;">
    <colgroup style="width: 100% ">
        <col style="width: 5%">
        <col style="width: 12%">
        <col style="width: 20%">
        <col style="width: 11%">
        <col style="width: 11%">
        <col style="width: 11%">
        <col style="width: 15%">
        <col style="width: 15%">
    </colgroup>
    <thead>
        <tr>
            <td colspan=7 style="border: 0px;">
                <div style="width: 8in !important; margin-left: auto; margin-right: auto; margin: 5px;">
                    <div style="float: right; width: 110px; !important; ">
                        <img src="/files/nssf_logo.png" style="height: 140px; width: 100px; !important" />
                    </div>
                    <div style="float: left; width: 140px;">
                        <img src="/files/tz_coa.png" style="height: 140px; width: 140px;" />
                    </div>
                    <div
                        style="display: block; margin: 0 auto; width: 430px; text-align: center; font-size: large; font-weight: bold;">
                        THE UNITED REPUBLIC OF TANZANIA<BR>
                        NATIONAL SOCIAL SECURITY<BR>
                        EMPLOYER CONTRIBUTION FORM
                    </div>
                </div>
                <br><br><br>
            </td>
        </tr>
        <tr>
            <td colspan=8 style="border: 0px;">
                <table style="border-spacing: 3px; border-collapse: separate; ">
                    <colgroup style="width: 100%">
                        <col style="width: 25%;">
                        <col style="width: 45%;">
                        <col style="width: 20%;">
                        <col style="width: 15%;">
                    </colgroup>
                    <tr style="background-color: lightgray;">
                        <td colspan="2">
                            <B>EMPLOYER DETAILS</B>
                        </td>
                        <td colspan="2">
                            <B>CONTRIBUTION DETAILS</B>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Employer NSSF Reg. No
                        </td>
                        <td>
                            <div style="float: left; width: 50%; border: 1px solid black">
                                &nbsp;{%= data[0][("employer_number")] %}
                            </div>
                        </td>
                        <td>
                            Contribution Month
                        </td>
                        <td>
                            <div style="float: left; width: 100%; border: 1px solid black">
                                &nbsp;{%= data[0][("contribution_month")] %}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Employer Name
                        </td>
                        <td>
                            <div style="float: left; width: 100%; border: 1px solid black">
                                &nbsp;{%= data[0][("company")] %}
                            </div>
                        </td>
                        <td>
                            Amount
                        </td>
                        <td>
                            <div style="float: left; width: 100%; border: 1px solid black">
                                &nbsp;{%= total_nssf.toLocaleString(undefined, {minimumFractionDigits: 2,
                                maximumFractionDigits: 2}) %}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Postal Address
                        </td>
                        <td>
                            <div style="float: left; width: 100%; border: 1px solid black">
                                &nbsp;P. O. Box {%= data[0][("pobox")] %}, {%= data[0][("city")] %}
                            </div>
                        </td>
                        <td>
                            Control Number
                        </td>
                        <td>
                            <div style="float: left; width: 100%; border: 1px solid black">
                                &nbsp;{%= data[0][("control_no")] %}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Physical Address
                        </td>
                        <td>
                            <div style="float: left; width: 100%; border: 1px solid black">
                                &nbsp;{%= data[0][("address")] %}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Telephone/Mobile Number
                        </td>
                        <td>
                            <div style="float: left; width: 50%; border: 1px solid black">
                                &nbsp;
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        <tr>
        <tr style="background-color: lightgray; ">
            <th style="border: 1px solid black; text-align: center; color: black;" rowspan="2">S/No</th>
            <th style="border: 1px solid black; text-align: center; color: black;" rowspan="2">MEMBERSHIP NO</th>
            <th style="border: 1px solid black; text-align: center; color: black;" rowspan="2">NATIONAL ID NUMBER</th>
            <th style="border: 1px solid black; text-align: center; color: black;" colspan="3">INSURED PERSON NAME</th>
            <th style="border: 1px solid black; text-align: center; color: black;" rowspan="2">WAGES (GROSS SALARY)</th>
            <th style="border: 1px solid black; text-align: center; color: black;" rowspan="2">TOTAL CONTRIBUTION</th>
        </tr>
        <tr style="background-color: lightgray; ">
            <th style="border: 1px solid black; text-align: center; color: black;">FIRST</th>
            <th style="border: 1px solid black; text-align: center; color: black;">MIDDLE</th>
            <th style="border: 1px solid black; text-align: center; color: black;">SURNAME</th>
        </tr>
    </thead>
    <tbody>
        {% for(var i=0, l=data.length; i<=l-1; i++) { %} <tr>
            <td style="border: 1px solid black;">{%= i+1 %}</td>
            <td style="border: 1px solid black;">{%= data[i][("membership_no")] %}</td>
            <td style="border: 1px solid black;">{%= data[i][("national_id")] %}</td>
            <td style="border: 1px solid black;">{%= data[i][("first_name")] %}</td>
            <td style="border: 1px solid black;">{%= data[i][("middle_name")] %}</td>
            <td style="border: 1px solid black;">{%= data[i][("last_name")] %}</td>
            <td style="border: 1px solid black; text-align: right">
                {%= data[i][("gross_pay")].toLocaleString(undefined, {minimumFractionDigits: 2,
                maximumFractionDigits:2}) %}
            </td>
            <td style="border: 1px solid black; text-align: right">
                {%= data[i][("contribution")].toLocaleString(undefined, {minimumFractionDigits: 2,
                maximumFractionDigits: 2}) %}
            </td>
            </tr>
            {% var page_total = page_total + data[i][("contribution")] %}
            {% if ((i+1)%20 == 0) { %}
    </tbody>
    <tfoot>
        <tr style="background-color: lightgray;">
            <th style="border: 1px solid black; text-align: center; color: black; text-align: right;" colspan="7">
                <b>PAGE TOTAL</b>
            </th>
            <th style="border: 1px solid black; text-align: center; color: black; text-align: right">
                <b>
                    {%= page_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}
                </b>
            </th>
        </tr>
        <tr>
            <th style="border: 1px solid black; text-align: center; color: black; height: 100px; vertical-align: bottom !important;"
                colspan="5">
                EMPLOYER OFFICE STAMP
            </th>
            <th style="border: 1px solid black; text-align: center; color: black; height: 100px; vertical-align: bottom !important;"
                colspan="3">
                NSSF OFFICE STAMP
            </th>
        </tr>
    </tfoot>
    </table>
    <div class="page-break"></div>
    <table style="margin-left: auto; margin-right: auto; border: 0px solid black;">
        <colgroup style="width: 100% ">
            <col style="width: 5%">
            <col style="width: 12%">
            <col style="width: 20%">
            <col style="width: 11%">
            <col style="width: 11%">
            <col style="width: 11%">
            <col style="width: 15%">
            <col style="width: 15%">
        </colgroup>
        <thead>
            <tr>
                <td colspan=7 style="border: 0px;">
                    <div style="width: 8in !important; margin-left: auto; margin-right: auto; margin: 5px;">
                        <div style="float: right; width: 110px; !important; ">
                            <img src="/files/nssf_logo.png" style="height: 140px; width: 100px; !important" />
                        </div>
                        <div style="float: left; width: 140px;">
                            <img src="/files/tz_coa.png" style="height: 140px; width: 140px;" />
                        </div>
                        <div
                            style="display: block; margin: 0 auto; width: 430px; text-align: center; font-size: large; font-weight: bold;">
                            THE UNITED REPUBLIC OF TANZANIA<BR>
                            NATIONAL SOCIAL SECURITY<BR>
                            EMPLOYER CONTRIBUTION FORM
                        </div>
                    </div>
                    <br><br><br>
                </td>
            </tr>
            <tr>
                <td colspan=8 style="border: 0px;">
                    <table style="border-spacing: 3px; border-collapse: separate; ">
                        <colgroup style="width: 100%">
                            <col style="width: 25%;">
                            <col style="width: 45%;">
                            <col style="width: 20%;">
                            <col style="width: 15%;">
                        </colgroup>
                        <tr style="background-color: lightgray;">
                            <td colspan="2">
                                <B>EMPLOYER DETAILS</B>
                            </td>
                            <td colspan="2">
                                <B>CONTRIBUTION DETAILS</B>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Employer NSSF Reg. No
                            </td>
                            <td>
                                <div style="float: left; width: 50%; border: 1px solid black">
                                    &nbsp;{%= data[0][("employer_number")] %}
                                </div>
                            </td>
                            <td>
                                Contribution Month
                            </td>
                            <td>
                                <div style="float: left; width: 100%; border: 1px solid black">
                                    &nbsp;{%= data[0][("contribution_month")] %}
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Employer Name
                            </td>
                            <td>
                                <div style="float: left; width: 100%; border: 1px solid black">
                                    &nbsp;{%= data[0][("company")] %}
                                </div>
                            </td>
                            <td>
                                Amount
                            </td>
                            <td>
                                <div style="float: left; width: 100%; border: 1px solid black">
                                    &nbsp;{%= total_nssf.toLocaleString(undefined, {minimumFractionDigits: 2,
                                    maximumFractionDigits: 2}) %}
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Postal Address
                            </td>
                            <td>
                                <div style="float: left; width: 100%; border: 1px solid black">
                                    &nbsp;P. O. Box {%= data[0][("pobox")] %}, {%= data[0][("city")] %}
                                </div>
                            </td>
                            <td>
                                Control Number
                            </td>
                            <td>
                                <div style="float: left; width: 100%; border: 1px solid black">
                                    &nbsp;{%= data[0][("control_no")] %}
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Physical Address
                            </td>
                            <td>
                                <div style="float: left; width: 100%; border: 1px solid black">
                                    &nbsp;{%= data[0][("address")] %}
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Telephone/Mobile Number
                            </td>
                            <td>
                                <div style="float: left; width: 50%; border: 1px solid black">
                                    &nbsp;
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            <tr>
            <tr style="background-color: lightgray; ">
                <th style="border: 1px solid black; text-align: center; color: black;" rowspan="2">S/No</th>
                <th style="border: 1px solid black; text-align: center; color: black;" rowspan="2">MEMBERSHIP NO</th>
                <th style="border: 1px solid black; text-align: center; color: black;" rowspan="2">NATIONAL ID NUMBER
                </th>
                <th style="border: 1px solid black; text-align: center; color: black;" colspan="3">INSURED PERSON NAME
                </th>
                <th style="border: 1px solid black; text-align: center; color: black;" rowspan="2">WAGES (GROSS SALARY)
                </th>
                <th style="border: 1px solid black; text-align: center; color: black;" rowspan="2">TOTAL CONTRIBUTION
                </th>
            </tr>
            <tr style="background-color: lightgray; ">
                <th style="border: 1px solid black; text-align: center; color: black;">FIRST</th>
                <th style="border: 1px solid black; text-align: center; color: black;">MIDDLE</th>
                <th style="border: 1px solid black; text-align: center; color: black;">SURNAME</th>
            </tr>
        </thead>
        <tbody>
            {% } %}
            {% } %}
        </tbody>
        <tfoot>
            <tr style="background-color: lightgray;">
                <th style="border: 1px solid black; text-align: center; color: black; text-align: right;" colspan="7">
                    <b>PAGE TOTAL</b>
                </th>
                <th style="border: 1px solid black; text-align: center; color: black; text-align: right">
                    <b>
                        {%= page_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})
                        %}
                    </b>
                </th>
            </tr>
            <tr style="background-color: lightgray;">
                <th style="border: 1px solid black; text-align: center; color: black; text-align: right;" colspan="7">
                    <b>GRAND TOTAL</b>
                </th>
                <th style="border: 1px solid black; text-align: center; color: black; text-align: right">
                    <b>
                        {%= total_nssf.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})
                        %}
                    </b>
                </th>
            </tr>
            <tr>
                <th style="border: 1px solid black; text-align: center; color: black; height: 100px; vertical-align: bottom !important;"
                    colspan="5">
                    EMPLOYER OFFICE STAMP
                </th>
                <th style="border: 1px solid black; text-align: center; color: black; height: 100px; vertical-align: bottom !important;"
                    colspan="3">
                    NSSF OFFICE STAMP
                </th>
            </tr>
        </tfoot>
    </TABLE>