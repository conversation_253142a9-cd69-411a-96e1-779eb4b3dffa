{"add_total_row": 0, "columns": [], "creation": "2020-02-17 17:35:48.735318", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [], "idx": 0, "is_standard": "Yes", "modified": "2021-07-09 17:07:47.693777", "modified_by": "Administrator", "module": "Payware", "name": "NSSF-CON.05", "owner": "Administrator", "prepared_report": 0, "query": "SELECT ss.employee AS emp_id, \r\n       emp.pension_fund_number AS membership_no, \r\n       emp.national_identity AS national_id, \r\n       substring_index(ss.employee_name,\" \",1) AS first_name, \r\n       substring_index(substring_index(ss.employee_name,\" \",2),\" \",-1) AS middle_name, \r\n       substring_index(ss.employee_name,\" \",-1) AS last_name, \r\n       SUM(ss.gross_pay) AS gross_pay, \r\n       SUM(sd.amount * 2) AS contribution,  \r\n       ss.company AS \"Company::\", \r\n       if(cp.p_o_box != '', cp.p_o_box, pobox.value) as \"pobox\", \r\n       if(cp.city != '', cp.city, city.value) as \"city\", \r\n       CONCAT(plot.value, '/', block.value, ' ', street.value) as address,\r\n       social.value as employer_number,\r\n       control_no.value as control_no,\r\n       date_format(ss.start_date, '%%M %%Y') AS contribution_month\r\nFROM `tabSalary Slip` ss LEFT OUTER JOIN `tabSalary Detail` sd ON sd.parent = ss.name \r\n                         LEFT OUTER JOIN `tabEmployee` emp ON ss.employee = emp.name AND emp.pension_fund = 'NSSF' \r\n\t\t\t\t         INNER JOIN `tabCompany` cp ON ss.company = cp.company_name \r\n                         LEFT OUTER JOIN `tabNSSF Payments` np ON np.payroll_month BETWEEN %(from_date)s AND %(to_date)s \r\n                         JOIN `tabSingles` social ON   social.doctype = \"Payware Settings\" AND social.field = \"social_security_fund_registration_number\"\r\n                         JOIN `tabSingles` pobox ON   pobox.doctype = \"Payware Settings\" AND pobox.field = \"p_o_box\"\r\n                         JOIN `tabSingles` city ON   city.doctype = \"Payware Settings\" AND city.field = \"city\"\r\n                         JOIN `tabSingles` control_no ON   control_no.doctype = \"Payware Settings\" AND control_no.field = \"social_security_control_number\"\r\n                         JOIN `tabSingles` plot ON   plot.doctype = \"Payware Settings\" AND plot.field = \"plot_number\"\r\n                         JOIN `tabSingles` block ON   block.doctype = \"Payware Settings\" AND block.field = \"block_number\"\r\n                         JOIN `tabSingles` street ON   street.doctype = \"Payware Settings\" AND street.field = \"street\"\r\nWHERE sd.salary_component = 'NSSF' \r\nAND   ss.docstatus = 1 \r\nAND   ss.start_date >= %(from_date)s\r\nAND   ss.end_date <= %(to_date)s\r\nGROUP BY ss.employee_name, ss.designation, emp.gender, emp.date_of_birth \r\nHAVING  SUM(ss.gross_pay) > 0", "ref_doctype": "<PERSON><PERSON>", "report_name": "NSSF-CON.05", "report_type": "Query Report", "roles": [{"role": "System Manager"}, {"role": "Accounts Manager"}, {"role": "Accounts User"}, {"role": "HR User"}, {"role": "HR Manager"}]}