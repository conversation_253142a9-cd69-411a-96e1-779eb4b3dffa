<style>
.print-format {
        padding-left: 10mm;
        padding-right: 5mm;
        padding-top: 0mm;
		font-size: 9pt;	
}

.print-format td, .print-format th {
    vertical-align: top !important;
    padding: 1px !important;
}

.tbody { display: block; page-break-before: avoid; }
.tbody { display: block; page-break-after: always; }

@media screen {
	.print-format {
		padding-left: 10mm;
        padding-right: 5mm;
        padding-top: 0mm;
		font-size: 9pt;
		width: 8.5in;
		height: 11in;
		}
	
	.print-format td, .print-format th {
    vertical-align: top !important;
    padding: 1px !important;
	}
	
    .tbody { display: block; page-break-before: avoid; }
    .tbody { display: block; page-break-after: always; }

}
</style>

{%	var from_date = filters.from_date %}
{%	var year = from_date[0] + from_date[1] + from_date[2] + from_date[3] %}
{%	var pmethod = data[0][ __("Payment Method")] + " (" + data[0][ __("Ref No.")] + ")" %}
{%  var page_no = 1 %}

{% var total_nssf = 0  %}
{% var page_total = 0  %}
{% for(var i=0, l=data.length; i<=l-1; i++) { %} 
	{% var total_nssf = total_nssf + data[i][ __("Contribution")]  %}
{% } %}

<table class="table table-bordered">
	<colgroup>
		<col style="width: 3%">
		<col style="width: 28%">
		<col style="width: 23%">
		<col style="width: 12%">
		<col style="width: 10%">
		<col style="width: 12%">
		<col style="width: 12%">
	</colgroup>
	<thead>
		<tr>
			<td colspan=7  style="border: 0px;">
				<p class="manifest-header text-center">
					<img src="/files/nssf conf.5 letter head.png" style="width: 100%;"/>
				</p>
				<!-- <table border=0 style="border-spacing: 0; border-collapse: collapse;">
					<colgroup style="width: 780px;">
						<col style="width: 510px;">
						<col style="width: 270px;">
					</colgroup>
					<tr>
						<td style="border: 0px;"> 
							<table border=0 style="border-spacing: 0; border-collapse: collapse;">
								<colgroup style="width: 510px;">
									<col style="width: 180px;">
									<col style="width: 330px;">
								</colgroup>
								<tr>
									<td style="border: 0px;"><B>Employer Name : </B></td>
									<td style="border: 0px;">{%= data[0][ __("Company")] %}</td>
								</tr>
								<tr>
									<td style="border: 0px;"><B>Address : </B></td>
									<td style="border: 0px;">P. O. Box {%= data[0][ __("P O Box")] %}<br>{%= data[0][ __("City")] %}<br>Tanzania</td>
								</tr>
							</table>
							<table border=0 style="border-spacing: 0; border-collapse: collapse;">
								<colgroup style="width: 510px;">
									<col style="width: 255px;">
									<col style="width: 255px;">
								</colgroup>
								<tr>
									<td style="border: 0px;"><B>Employer&apos;s Registration Number : </B></td>
									<td style="border: 0px;">
										<div style="border: 1px solid black; padding: 2px; text-align: center; width: 200px; height: 18px;">
											<span>{%= data[0][ __("NSSF Employer No")] %}</span>
										<div>
									</td>
								</tr>
								<tr>
									<td style="border: 0px;"><B>Month of Contribution : </B></td>
									<td style="border: 0px;">
										<div>
											<div style="float: left; width: 100px; border: 1px solid black; padding: 2px;text-align: center;  height: 18px;">
												{%= data[0][ __("Contribution Month")] %}
											</div>
											<div style="float: left; padding: 2px; text-align: center;">
												<b>&nbsp;Year : </b>
											</div>
											<div style="float: left; border: 1px solid black; padding: 2px; text-align: center;  height: 18px;">
												{%= data[0][ __("Contribution Year")] %}
											</div>
										</div>
									</td>
								</tr> 
								<tr>
									<td style="border: 0px;"><B>Regional/District Code No.: </B></td>
									<td style="border: 0px;">&nbsp;</td>
								</tr>
							</table>
						</td>
						<td style="border: 0px;"> 
							<table border=0 style="border-spacing: 0; border-collapse: collapse;">
								<colgroup style="width: 270px;">
									<col style="width: 135px;">
									<col style="width: 135px;">
								</colgroup>
								<tr>
									<td style="border: 0px;"><b>Page No:</b></td>
									<td style="border: 0px;">
										<div style="border: 1px solid black; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span>&nbsp;{%= page_no %}</span>
										<div>
									</td>
								</tr>
								<tr>
									<td style="border: 0px;"><b>Cheque No.: </b></td>
									<td style="border: 0px;">
										<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span>&nbsp;</span>
										<div>
									</td>
								</td>
								<tr>
									<td style="border: 0px;"><b>Amount T Shs.</b></td>
									<td style="border: 0px;">
										<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span><b>&nbsp;{%= total_nssf.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</b></span>
										<div>
									</td>
								</td>
								<tr>
									<td style="border: 0px;"><b>Cash T Shs.</b></td>
									<td style="border: 0px;">
										<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span>&nbsp;</span>
										<div>
									</td>
								</td>
								<tr>
									<td style="border: 0px;"><b>Receipt No.: </b></td>
									<td style="border: 0px;">
										<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span>&nbsp;</span>
										<div>
									</td>
								</td>
								<tr>
									<td style="border: 0px;"><b>Date: </b></td>
									<td style="border: 0px;">
										<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
											<span>&nbsp;{%= frappe.datetime.str_to_user(data[0][ __("Payment Date")]) %}</span>
									</td>
								</td>
							</table>
						</td>
					</tr>
				</table>		 -->
			</td>
		<tr>
		<tr>
			<th style="text-align: center">SR #</th>
			<th style="text-align: center">MEMBER'S NAME</th>
			<th style="text-align: center">NATIONAL ID NO. </th>
			<th style="text-align: center">WAGES</th>
			<th style="text-align: center">MEMBERSHIP NO.</th>
			<th style="text-align: center">AMOUNT<br>TZS</th>
			<th style="text-align: center">REMARKS</th>
		</tr>
	</thead>		
	<!-- <tbody>
		{% for(var i=0, l=data.length; i<=l-1; i++) { %} 
				<tr">	
					<td>{%= i+1 %}</td>
					<td>{%= data[i][ __("Employee Name")] %}</td>
					<td>{%= data[i][ __("National ID")] %}</td>
					<td style="text-align: right">{%= data[i][ __("Gross Pay")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}  </td>
					<td style="text-align: center">{%= data[i][ __("Membership No")] %} </td>
					<td style="text-align: right"> {%= data[i][ __("Contribution")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %} </td>
					<td></td>		
				</tr>
			{% var page_total = page_total + data[i][ __("Contribution")]  %}	
			{% if ((i+1)%30 == 0) { %} 
					</tbody>
					<tfoot>
						<tr>
							<th style="text-align: right" colspan=5><b>PAGE TOTAL</b></th>
							<th style="text-align: right"><b>{%= page_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</b></th>
							<th>&nbsp;</th>
						</tr>
					</tfoot>
				</table>
				<div class="page-break"></div>
				{% var page_total = 0  %}
				{% page_no = page_no + 1 %}
				<table class="table table-bordered">
					<colgroup>
						<col style="width: 3%">
						<col style="width: 28%">
						<col style="width: 23%">
						<col style="width: 12%">
						<col style="width: 10%">
						<col style="width: 12%">
						<col style="width: 12%">
									</colgroup>
					<thead>
						<tr style="border: 0px;">
							<td colspan=7  style="border: 0px;">
								<p class="manifest-header text-center">
									<img src="/files/nssf conf.5 letter head.png" style="width: 100%;"/>
								</p>
								<table border=0 style="border-spacing: 0; border-collapse: collapse;">
									<colgroup style="width: 780px;">
										<col style="width: 510px;">
										<col style="width: 270px;">
									</colgroup>
									<tr>
										<td style="border: 0px;"> 
											<table border=0 style="border-spacing: 0; border-collapse: collapse;">
												<colgroup style="width: 510px;">
													<col style="width: 180px;">
													<col style="width: 330px;">
												</colgroup>
												<tr>
													<td style="border: 0px;"><B>Employer Name : </B></td>
													<td style="border: 0px;">{%= data[0][ __("Company")] %}</td>
												</tr>
												<tr>
													<td style="border: 0px;"><B>Address : </B></td>
													<td style="border: 0px;">P. O. Box {%= data[0][ __("P O Box")] %}<br>{%= data[0][ __("City")] %}<br>Tanzania</td>
												</tr>
											</table>
											<table border=0 style="border-spacing: 0; border-collapse: collapse;">
												<colgroup style="width: 510px;">
													<col style="width: 255px;">
													<col style="width: 255px;">
												</colgroup>
												<tr>
													<td style="border: 0px;"><B>Employer&apos;s Registration Number : </B></td>
													<td style="border: 0px;">
														<div style="border: 1px solid black; padding: 2px; text-align: center; width: 200px; height: 18px;">
															<span>{%= data[0][ __("NSSF Employer No")] %}</span>
														<div>
													</td>
												</tr>
												<tr>
													<td style="border: 0px;"><B>Month of Contribution : </B></td>
													<td style="border: 0px;">
														<div>
															<div style="float: left; width: 100px; border: 1px solid black; padding: 2px;text-align: center;  height: 18px;">
																{%= data[0][ __("Contribution Month")] %}
															</div>
															<div style="float: left; padding: 2px; text-align: center;">
																<b>&nbsp;Year : </b>
															</div>
															<div style="float: left; border: 1px solid black; padding: 2px; text-align: center;  height: 18px;">
																{%= data[0][ __("Contribution Year")] %}
															</div>
														</div>
													</td>
												</tr> 
												<tr>
													<td style="border: 0px;"><B>Regional/District Code No.: </B></td>
													<td style="border: 0px;">&nbsp;</td>
												</tr>
											</table>
										</td>
										<td style="border: 0px;"> 
											<table border=0 style="border-spacing: 0; border-collapse: collapse;">
												<colgroup style="width: 270px;">
													<col style="width: 135px;">
													<col style="width: 135px;">
												</colgroup>
												<tr>
													<td style="border: 0px;"><b>Page No:</b></td>
													<td style="border: 0px;">
														<div style="border: 1px solid black; padding: 2px; text-align: center; width: 135px; height: 18px;">
															<span>&nbsp;{%= page_no %}</span>
														<div>
													</td>
												</tr>
												<tr>
													<td style="border: 0px;"><b>Cheque No.: </b></td>
													<td style="border: 0px;">
														<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
															<span>&nbsp;</span>
														<div>
													</td>
												</td>
												<tr>
													<td style="border: 0px;"><b>Amount T Shs.</b></td>
													<td style="border: 0px;">
														<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
															<span><b>&nbsp;{%= total_nssf.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</b></span>
														<div>
													</td>
												</td>
												<tr>
													<td style="border: 0px;"><b>Cash T Shs.</b></td>
													<td style="border: 0px;">
														<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
															<span>&nbsp;</span>
														<div>
													</td>
												</td>
												<tr>
													<td style="border: 0px;"><b>Receipt No.: </b></td>
													<td style="border: 0px;">
														<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
															<span>&nbsp;</span>
														<div>
													</td>
												</td>
												<tr>
													<td style="border: 0px;"><b>Date: </b></td>
													<td style="border: 0px;">
														<div style="border-bottom: double; padding: 2px; text-align: center; width: 135px; height: 18px;">
															<span>&nbsp;{%= frappe.datetime.str_to_user(data[0][ __("Payment Date")]) %}</span>
													</td>
												</td>
											</table>
										</td>
									</tr>
								</table>		
							</td>
						<tr>
						<tr>
							<th style="text-align: center">SR #</th>
							<th style="text-align: center">MEMBER'S NAME</th>
							<th style="text-align: center">NATIONAL ID NO. </th>
							<th style="text-align: center">WAGES</th>
							<th style="text-align: center">MEMBERSHIP NO.</th>
							<th style="text-align: center">AMOUNT<br>TZS</th>
							<th style="text-align: center">REMARKS</th>
						</tr>
					</thead>			
			{% } %}
		{% } %}
	</tbody> -->
<!-- 	<tfoot>
		<tr>
			<th style="text-align: right" colspan=5><b>PAGE TOTAL</b></th>
			<th style="text-align: right"><b>{%= page_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</b></th>
			<th>&nbsp;</th>
		</tr>
		<tr>
			<th style="text-align: right" colspan=5><b>GRAND TOTAL</b></th>
			<th style="text-align: right"><b>{%= total_nssf.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</b></th>
			<th>&nbsp;</th>
		</tr>
		<tr>
			<td colspan=3 align=center style="border: 0px;">		
				<div style="border: 1px solid black; padding: 2px; text-align: center; width: 300px; height: 25px; margin-bottom: 2px; margin-top: 4px;">
					<span><b>EMPLOYER'S SIGNATURE</b></span>
				</div>
				<div style="border: 1px solid black; padding: 2px; text-align: center; width: 300px; height: 75px; margin-bottom: 2px; margin-top: 2px;">
					<span><b>&nbsp;</b></span>
				</div>
			</td>
			<td colspan=2 align=center style="border: 0px;">
			
				<div style="border: 1px solid black; padding: 2px; text-align: center; width: 150px; height: 25px; margin-bottom: 2px; margin-top: 4px;">
					<span><b>DATE</b></span>
				</div>
				<div style="border: 1px solid black; padding: 2px; text-align: center; width: 150px; height: 75px; margin-bottom: 2px;  margin-top: 2px;">
					<span><b>&nbsp;</b></span>
				</div>		
			</td>
			<td colspan=2 align=center style="border: 0px;"></td>
		</tr>
		<tr>
			<td colspan=7 style="border: 0px;">
				<div style="margin-top: 2px;">
					<div style="float: left; width: 50px; border: 0px solid black; padding: 2px;text-align: center;  height: 18px;">
						<b><i>NOTE:</i></b>
					</div>
					<div style="float: left; width: 5px; padding: 2px; text-align: center;">
						&nbsp;
					</div>
					<div style="float: left; border: 0px solid black; padding: 2px; text-align: left;  height: 55px;">
						<ul>
							<li><i>To be users for NSSF registered members and full contribution of 20% should be shown</i></li>
							<li><i>Each page total must be shown seperatly</i></li>
							<li><i>Summary of all page totals must be shown on the last page</i></li>
						</ul>
					</div>
				</div>
			</td>
		</tr>
	</tfoot> -->
</table>
<!--
CONTRIBUTION FOR PERMENANT EMPLOYEES (MANDATORY)	
<table class="table table-bordered">
	<!--<colgroup>
		<col style="width: 3%">
		<col style="width: 17%">
		<col style="width: 25%">
		<col style="width: 12%">
		<col style="width: 11%">
		<col style="width: 12%">
		<col style="width: 20%">
	</colgroup>
	<thead>
		<tr>
			<th style="text-align: center">{%= __("") %}</th>
			<th style="text-align: center">{%= __("Emp ID") %}</th>
			<th style="text-align: center">{%= __("Insured Person's Name") %}</th>
			<th style="text-align: center">{%= __("Total Gross") %}</th>
			<th style="text-align: center">{%= __("Membership<br>No") %}</th>
			<th style="text-align: center">{%= __("Contributions<br>Tshs (20%)") %}</th>
			<th style="text-align: center">{%= __("Remarks") %}</th>
		</tr>
	</thead>
	{% var ptotal = 0  %}
	{% var pdata = [] %}
    <tbody>
        {% for(var i=0, l=data.length; i<l-1; i++) { %}  	 
		<tr>
            <td>{%= i+1 %}</td>
            <td>{%= data[i][ __("Emp ID")] %}</td>
            <td>{%= data[i][ __("Employee Name")] %}</td>
            <td style="text-align: right">{%= data[i][ __("Gross Pay")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %} </td>
            <td style="text-align: center">{%= data[i][ __("Membership No")] %} </td>
            <td style="text-align: right">{%= data[i][ __("Contribution")].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %} </td>
			<td></td>
        </tr>
		{% var ptotal = ptotal + data[i][ __("Contribution")]  %}
		{% if ((i+1)%20 == 0) { %} 
        <tr>
			<th style="text-align: right" colspan=5><b>TOTAL</b></td>
			<th style="text-align: right"><b>{%= ptotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</b></td>
			<th>&nbsp;</td>
        </tr>
				
	</tbody>
</table>
<p class="text-left">
<br><br><br>
Employer's Signature: ___________________________________<br>
<strong>NOTE:</strong><br>
*To be used for NSSF registered numbers and full contribution of 20% should be shown<br>
*Each page total must be shown separately<br>
*Summary of all page totals must be shown on last page<br>
</p>
 {% pdata.push(ptotal) %}
 {% var ptotal = 0  %}
<div class="page-break"></div>

<p class="manifest-header text-center">
   <img src="/private/files/nssf conf.5 letter head.png" style="width: 35%;"/>
</p>

<table class="table">
	<tbody>
		<tr>
			<td width="50%"> 
				<strong>Employer Name : </strong>{%= data[0][ __("Company")] %}<br>
				<strong>Address : </strong>{%= data[0][ __("P O Box")] %} {%= data[0][ __("City")] %}<br>
				<strong>Employer&apos;s Registration Number : </strong>{%= data[0][ __("NSSF Employer No")] %}<br>
				<strong>Month of Contribution : </strong>{%= data[0][ __("Contribution Month")] %}<br>
				<strong>Regional/District Office : </strong>{%= data[0][ __("NSSF Office")] %}<br>
			</td>
			<td width="50%"> 
				<strong>Chq/Mo/Po No.: </strong>{%= pmethod %}<br>
				<strong>Date of Chq/Mo/Po: </strong>{%= frappe.datetime.str_to_user(data[0][ __("Payment Date")]) %}<br>
				<strong>Bank/Post Office Branch : </strong><br>
				<strong>Year : </strong>{%= year %}<br>
			</td>
		</tr>
	</tbody>
</table>	 
CONTRIBUTION FOR PERMENANT EMPLOYEES (MANDATORY)
				 
				 <table class="table table-bordered">
					<!--<colgroup>
						<col style="width: 3%">
						<col style="width: 17%">
						<col style="width: 25%">
						<col style="width: 12%">
						<col style="width: 11%">
						<col style="width: 12%">
						<col style="width: 20%">
					</colgroup>
                  <thead>
                    <tr>
						<th style="text-align: center">{%= __("") %}</th>
						<th style="text-align: center">{%= __("Emp ID") %}</th>
						<th style="text-align: center">{%= __("Insured Person's Name") %}</th>
						<th style="text-align: center">{%= __("Total Gross") %}</th>
						<th style="text-align: center">{%= __("Membership<br>No") %}</th>
						<th style="text-align: center">{%= __("Contributions<br>Tshs (20%)") %}</th>
						<th style="text-align: center">{%= __("Remarks") %}</th>
					</tr>
                  </thead>
				  <tbody>
				
			 {% } %}
			 
        {% } %}

        </tbody>
		<tr>
                <th style="text-align: right" colspan=5><b>TOTAL</b></td>
                <th style="text-align: right"><b>{%= ptotal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</b></td>
				<th>&nbsp;</td>
        </tr>
		{% pdata.push(ptotal) %}
</table>
<p class="text-left">
<br><br><br>
Employer's Signature: ___________________________________<br>
<strong>NOTE:</strong><br>
*To be used for NSSF registered numbers and full contribution of 20% should be shown<br>
*Each page total must be shown separately<br>
*Summary of all page totals must be shown on last page<br>
</p>
<div class="page-break"></div>

<p class="manifest-header text-center">
   <img src="/private/files/nssf conf.5 letter head.png" style="width: 35%;"/>
</p>
<table class="table">
	<tbody>
		<tr>
			<td width="50%"> 
				<strong>Employer Name : </strong>{%= data[0][ __("Company")] %}<br>
				<strong>Address : </strong>{%= data[0][ __("P O Box")] %} {%= data[0][ __("City")] %}<br>
				<strong>Employer&apos;s Registration Number : </strong>{%= data[0][ __("NSSF Employer No")] %}<br>
				<strong>Month of Contribution : </strong>{%= data[0][ __("Contribution Month")] %}<br>
				<strong>Regional/District Office : </strong>{%= data[0][ __("NSSF Office")] %}<br>
			</td>
			<td width="50%"> 
				<strong>Chq/Mo/Po No.: </strong>{%= pmethod %}<br>
				<strong>Date of Chq/Mo/Po: </strong>{%= frappe.datetime.str_to_user(data[0][ __("Payment Date")]) %}<br>
				<strong>Bank/Post Office Branch : </strong><br>
				<strong>Year : </strong>{%= year %}<br>
			</td>
		</tr>
	</tbody>
</table>
PAGE SUMMARY
   
   <table class="table table-bordered" style="width: 50%">
		<colgroup>
			<col style="width: 25%">
			<col style="width: 25%">
		</colgroup>
		<thead>
			<tr>
				<th style="text-align: center">{%= __("PAGE NUMBER") %}</th>
				<th style="text-align: center">{%= __("AMOUNT") %}</th>
			</tr>
		</thead>
		<tbody>
	{% var grand_total = 0 %}
	{% for(var i=0, l=pdata.length; i<l; i++) { %}  
			<tr>
				<td>Page No. {%= i+1 %}</td>
				<td align="right">{%= pdata[i].toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</td>
			</tr>
			{% grand_total = grand_total + pdata[i] %}
	{% } %}
		</tbody>
		<tfoot>
			<tr>
				<th style="text-align: right">TOTAL</th>
				<th style="text-align: right">{%= grand_total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) %}</th>
			</tr>
		</tfoot>-->