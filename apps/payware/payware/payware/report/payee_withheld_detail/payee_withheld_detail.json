{"add_total_row": 0, "creation": "2019-04-10 11:12:57.549381", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2019-04-15 23:10:36.718193", "modified_by": "Administrator", "module": "Payware", "name": "Payee Withheld Detail", "owner": "Administrator", "prepared_report": 0, "query": "select \n  ss.start_date as \"Start Date::100\",\n  ss.employee_name as \"Employee Name::180\",\n  ss.designation as \"Job Title::140\",\n  sum(sdbasic.amount) as \"Basic Salary:Currency:120\",\n  sum(sdhousing.amount) as \"Housing Allowance:Currency:120\",\n  sum(sdti.amount - sdbasic.amount - sdhousing.amount) as \"Other Allw and Benefits:Currency:120\",\n  sum(ss.gross_pay) as \"Gross Pay:Currency:120\",\n  sum(sdnssf.amount) as \"NSSF:Currency:120\",\n  sum(sdpaye.amount) as \"PAYE:Currency:120\"\nfrom `tabSalary Slip` ss \nleft outer join `tabSalary Detail` sdbasic on sdbasic.parent = ss.name and sdbasic.salary_component = 'Basic'\nleft outer join `tabSalary Detail` sdhousing on sdhousing.parent = ss.name and sdhousing.salary_component = 'Housing'\nleft outer join `tabSalary Detail` sdti on sdti.parent = ss.name and sdti.salary_component = 'TI'\nleft outer join `tabSalary Detail` sdnssf on sdnssf.parent = ss.name and sdnssf.salary_component = 'NSSF'\nleft outer join `tabSalary Detail` sdpaye on sdpaye.parent = ss.name and sdpaye.salary_component = 'PAYE'\nleft join `tabEmployee` emp on ss.employee = emp.name\ngroup by ss.start_date, ss.employee_name, ss.designation, emp.gender, emp.date_of_birth\nhaving sum(ss.gross_pay) > 0", "ref_doctype": "<PERSON><PERSON>", "report_name": "Payee Withheld Detail", "report_type": "Query Report", "roles": [{"role": "System Manager"}]}