{"add_total_row": 0, "creation": "2020-07-30 15:30:10.509504", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "letter_head": "Payware Letterhead", "modified": "2020-07-31 08:51:55.864795", "modified_by": "Administrator", "module": "Payware", "name": "Payroll Accounting", "owner": "Administrator", "prepared_report": 0, "query": "SELECT ss.name as \"Salary Structure\",\n  sc.name as \"Salary Component\",\n  sc.type as \"Salary Component Type\",\n  ac.company as \"Company\",\n  ac.name as \"Account\",\n  ac.parent_account as \"Parent Account\",\n  ac.root_type as \"Root Type\",\n  ac.account_type as \"Account Type\"\nFROM `tabSalary Structure` ss\nINNER JOIN `tabSalary Detail` sd ON sd.parent = ss.name\nINNER JOIN `tabSalary Component` sc ON sc.name = sd.salary_component\nINNER JOIN `tabSalary Component Account` sca ON sca.parent = sc.name\nINNER JOIN `tabAccount` ac ON ac.name = sca.default_account AND ac.company = sca.company", "ref_doctype": "Salary Structure", "report_name": "Payroll Accounting", "report_type": "Query Report", "roles": [{"role": "HR User"}, {"role": "HR Manager"}]}