{"add_total_row": 1, "creation": "2019-03-15 11:14:37.548813", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2019-03-15 11:40:06.786464", "modified_by": "Administrator", "module": "Payware", "name": "SDL Report", "owner": "Administrator", "prepared_report": 0, "query": "SELECT ss.start_date as \"Salary Period Start\", \n       ss.end_date as \"Salary Period End\", \n       e.employee_name as \"Employee Name\", \n       ss.gross_pay as \"Employee Gross Pay\", \n       sd.amount as \"PAYE contribution\", \n       ss.gross_pay * ps.value / 100 as \"SDL contribution\"\nFROM   `tabSalary Slip` ss \n       INNER JOIN `tabSalary Detail` sd \n               ON ss.name = sd.parent \n                  AND sd.salary_component = \"PAYE\" \n       INNER JOIN `tabEmployee` e \n               ON ss.employee = e.name \n                  AND (e.relieving_date >= ss.start_date OR e.relieving_date is null)\n       INNER JOIN `tabSingles` ps \n               ON ps.doctype = \"Payware Settings\" \n                  AND ps.field = \"skills_development_levy\" \nWHERE  ss.docstatus = 1 \nGROUP  BY ss.start_date, \n          ss.end_date, \n          e.employee_name ", "ref_doctype": "<PERSON><PERSON>", "report_name": "SDL Report", "report_type": "Query Report", "roles": [{"role": "HR Manager"}, {"role": "HR User"}, {"role": "Employee"}, {"role": "System Manager"}]}