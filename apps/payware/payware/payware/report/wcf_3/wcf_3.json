{"add_total_row": 0, "creation": "2020-02-17 17:35:48.703452", "disable_prepared_report": 0, "disabled": 0, "docstatus": 0, "doctype": "Report", "idx": 0, "is_standard": "Yes", "modified": "2020-02-19 19:09:05.259302", "modified_by": "Administrator", "module": "Payware", "name": "WCF-3", "owner": "Administrator", "prepared_report": 0, "query": "select emp.wcf_number as \"wcf_number::120\", \n       emp.first_name as \"firstname::120\", \n       emp.middle_name as \"middlename::120\", \n       emp.last_name as \"lastname::120\", \n       emp.gender as \"gender::75\", \n       emp.date_of_birth as \"dob::70\", \n       sum(sd.amount) as \"basicpay::100\",\n       sum(ss.gross_pay) as \"grosspay::100\",\n       ss.designation as \"job_title::200\",\n       'PERMANENT' as \"employement_category::150\",\n       emp.national_identity as \"national_id::200\"\nfrom `tabSalary Slip` ss left join `tabSalary Detail` sd on sd.parent = ss.name \n                          left join `tabEmployee` emp on ss.employee = emp.name\nwhere sd.salary_component = 'Basic'\n  and ss.start_date >= %(from_date)s\n  and ss.end_date <= %(to_date)s\ngroup by ss.employee_name, ss.designation, emp.gender, emp.date_of_birth\nhaving sum(ss.gross_pay) > 0", "ref_doctype": "<PERSON><PERSON>", "report_name": "WCF-3", "report_type": "Query Report", "roles": [{"role": "System Manager"}]}