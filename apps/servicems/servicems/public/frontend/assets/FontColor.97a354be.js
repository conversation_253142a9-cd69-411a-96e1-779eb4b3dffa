import{_ as g,P as p,Q as C,r as u,o as n,k as i,l as s,m as _,p as k,q as b,e as a,c,F as m,A as f,R as x,s as v}from"./index.aff78e17.js";const y={name:"FontColor",props:["editor"],components:{Popover:p,Tooltip:C},methods:{setBackgroundColor(t){t.name!="Default"?this.editor.chain().focus().toggleHighlight({color:t.hex}).run():this.editor.chain().focus().unsetHighlight().run()},setForegroundColor(t){t.name!="Default"?this.editor.chain().focus().setColor(t.hex).run():this.editor.chain().focus().unsetColor().run()}},computed:{foregroundColors(){return[{name:"Default",hex:"#1F272E"},{name:"Yellow",hex:"#ca8a04"},{name:"Orange",hex:"#ea580c"},{name:"Red",hex:"#dc2626"},{name:"<PERSON>",hex:"#16a34a"},{name:"Blue",hex:"#1579D0"},{name:"Purple",hex:"#9333ea"},{name:"Pink",hex:"#db2777"}]},backgroundColors(){return[{name:"Default",hex:null},{name:"Yellow",hex:"#fef9c3"},{name:"Orange",hex:"#ffedd5"},{name:"Red",hex:"#fee2e2"},{name:"Green",hex:"#dcfce7"},{name:"Blue",hex:"#D3E9FC"},{name:"Purple",hex:"#f3e8ff"},{name:"Pink",hex:"#fce7f3"}]}}},P={class:"p-2"},B={class:"mt-1 grid grid-cols-8 gap-1"},F=["aria-label","onClick"],D={class:"mt-1 grid grid-cols-8 gap-1"},w=["aria-label","onClick"];function A(t,o,R,T,$,r){const d=u("Tooltip"),h=u("Popover");return n(),i(h,{transition:"default"},{target:s(({togglePopover:e,isOpen:l})=>[_(t.$slots,"default",k(b({onClick:()=>e(),isActive:l})))]),"body-main":s(()=>[a("div",P,[o[0]||(o[0]=a("div",{class:"text-sm text-gray-700"},"Text Color",-1)),a("div",B,[(n(!0),c(m,null,f(r.foregroundColors,e=>(n(),i(d,{class:"flex",key:e.name,text:e.name},{default:s(()=>[a("button",{"aria-label":e.name,class:"flex h-5 w-5 items-center justify-center rounded border text-base",style:x({color:e.hex}),onClick:l=>r.setForegroundColor(e)}," A ",12,F)]),_:2},1032,["text"]))),128))]),o[1]||(o[1]=a("div",{class:"mt-2 text-sm text-gray-700"},"Background Color",-1)),a("div",D,[(n(!0),c(m,null,f(r.backgroundColors,e=>(n(),i(d,{class:"flex",key:e.name,text:e.name},{default:s(()=>[a("button",{"aria-label":e.name,class:v(["flex h-5 w-5 items-center justify-center rounded border text-base text-gray-900",e.hex?"border-transparent":"border-gray-200"]),style:x({backgroundColor:e.hex}),onClick:l=>r.setBackgroundColor(e)}," A ",14,w)]),_:2},1032,["text"]))),128))])])]),_:3})}var E=g(y,[["render",A]]);export{E as default};
