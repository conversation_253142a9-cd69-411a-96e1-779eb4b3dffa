import{_ as d,S as m,T as g,U as L,r as i,o as f,c as p,m as D,p as c,q as h,f as l,l as a,V as _,D as v,F as w}from"./index.aff78e17.js";const V={name:"InsertLink",props:["editor"],components:{Button:m,FormControl:g,Dialog:L},data(){return{setLinkDialog:{url:"",show:!1}}},methods:{openDialog(){let t=this.editor.getAttributes("link").href;t&&(this.setLinkDialog.url=t),this.setLinkDialog.show=!0},setLink(t){t===""?this.editor.chain().focus().extendMarkRange("link").unsetLink().run():this.editor.chain().focus().extendMarkRange("link").setLink({href:t}).run(),this.setLinkDialog.show=!1,this.setLinkDialog.url=""},reset(){this.setLinkDialog=this.$options.data().setLinkDialog}}};function x(t,e,C,B,n,s){const r=i("FormControl"),u=i("Button"),k=i("Dialog");return f(),p(w,null,[D(t.$slots,"default",c(h({onClick:s.openDialog}))),l(k,{options:{title:"Set Link"},modelValue:n.setLinkDialog.show,"onUpdate:modelValue":e[3]||(e[3]=o=>n.setLinkDialog.show=o),onAfterLeave:s.reset},{"body-content":a(()=>[l(r,{type:"text",label:"URL",modelValue:n.setLinkDialog.url,"onUpdate:modelValue":e[0]||(e[0]=o=>n.setLinkDialog.url=o),onKeydown:e[1]||(e[1]=_(o=>s.setLink(o.target.value),["enter"]))},null,8,["modelValue"])]),actions:a(()=>[l(u,{variant:"solid",onClick:e[2]||(e[2]=o=>s.setLink(n.setLinkDialog.url)),class:"w-full"},{default:a(()=>e[4]||(e[4]=[v(" Save ")])),_:1})]),_:1},8,["modelValue","onAfterLeave"])],64)}var R=d(V,[["render",x]]);export{R as default};
