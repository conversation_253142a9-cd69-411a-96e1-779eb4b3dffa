import frappe
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields


def execute():
    fields = {
        "Customer": [
            {
                "fieldname": "vfd_details",
                "label": "VFD Details",
                "fieldtype": "Section Break",
                "no_copy": 1,
                "insert_after": "tax_withholding_category",
            },
            {
                "fieldname": "vfd_cust_id",
                "label": "VFD Customer ID",
                "fieldtype": "Data",
                "insert_after": "vfd_details",
                "reqd": 1,
                "no_copy": 1,
                "translatable": 1,
            },
            {
                "fieldname": "vfd_cust_id_type",
                "label": "VFD Customer ID Type",
                "fieldtype": "Select",
                "insert_after": "vfd_cust_id",
                "reqd": 1,
                "no_copy": 1,
                "translatable": 1,
                "options": "\n1- TIN\n2- Passport\n3- Driving License\n4- Voter ID\n5- Aadhaar\n6- Other",
            },
        ],
        "Sales Invoice": [
            {
                "fieldname": "generate_vfd",
                "label": "Generate VFD",
                "fieldtype": "Button",
                "insert_after": "due_date",
                "depends_on": "eval: doc.docstatus == 1 &&\
                    doc.is_not_vfd_invoice == 0 && \
                    doc.vfd_status != 'Success' && \
                    doc.is_return == 0",
                "allow_on_submit": 1,
            },
            {
                "fieldname": "vfd_details",
                "label": "VFD Details",
                "fieldtype": "Section Break",
                "insert_after": "authotp",
                "collapsible": 1,
                "no_copy": 1,
                "depends_on": 'eval: !in_list(frappe.user_roles, "Healthcare Receptionist")',
            },
            {
                "fieldname": "vfd_date",
                "label": "VFD Date",
                "fieldtype": "Date",
                "insert_after": "vfd_details",
                "allow_on_submit": 1,
                "no_copy": 1,
                "read_only": 1,
            },
            {
                "fieldname": "vfd_time",
                "label": "VFD Time",
                "fieldtype": "Time",
                "insert_after": "vfd_date",
                "allow_on_submit": 1,
                "no_copy": 1,
                "read_only": 1,
            },
            {
                "fieldname": "vfd_posting_info",
                "label": "VFD Posting Info",
                "fieldtype": "Link",
                "insert_after": "vfd_time",
                "options": "VFD Provider Posting",
                "allow_on_submit": 1,
                "no_copy": 1,
                "read_only": 1,
            },
            {
                "fieldname": "vfd_verification_url",
                "label": "VFD Verification URL",
                "fieldtype": "Data",
                "insert_after": "vfd_posting_info",
                "allow_on_submit": 1,
                "read_only": 1,
                "no_copy": 1,
                "translatable": 1,
                "length": "1000",
            },
            {
                "fieldname": "vfd_dc",
                "label": "VFD DC",
                "fieldtype": "Int",
                "no_copy": 1,
                "read_only": 1,
                "insert_after": "vfd_verification_url",
                "allow_on_submit": 1,
            },
            {
                "fieldname": "vfd_gc",
                "label": "VFD GC",
                "fieldtype": "Int",
                "insert_after": "vfd_dc",
                "allow_on_submit": 1,
                "no_copy": 1,
                "read_only": 1,
            },
            {
                "fieldname": "column_break_vfd",
                "label": "",
                "fieldtype": "Column Break",
                "insert_after": "vfd_gc",
                "no_copy": 1,
            },
            {
                "fieldname": "vfd_status",
                "label": "VFD Status",
                "fieldtype": "Select",
                "insert_after": "column_break_vfd",
                "allow_on_submit": 1,
                "default": "Not Sent",
                "in_standard_filter": 1,
                "translatable": 1,
                "no_copy": 1,
                "in_list_view": 1,
                "read_only": 1,
                "options": "Not Sent\nPending\nFailed\nSuccess",
            },
            {
                "fieldname": "is_not_vfd_invoice",
                "fieldtype": "Check",
                "label": "Is Not VFD Invoice",
                "insert_after": "vfd_status",
                "no_copy": 1,
            },
            {
                "fieldname": "is_auto_generate_vfd",
                "fieldtype": "Check",
                "label": "Is Auto Generate VFD",
                "insert_after": "is_not_vfd_invoice",
                "allow_on_submit": 1,
                "no_copy": 1,
            },
            {
                "fieldname": "vfd_cust_id_type",
                "fieldtype": "Data",
                "label": "VFD Cust ID Type",
                "insert_after": "is_auto_generate_vfd",
                "fetch_from": "customer.vfd_cust_id_type",
                "no_copy": 1,
                "translatable": 1,
                "fetch_if_empty": 1,
            },
            {
                "fieldname": "vfd_cust_id",
                "label": "VFD Cust ID",
                "fieldtype": "Data",
                "insert_after": "vfd_cust_id_type",
                "fetch_from": "customer.vfd_cust_id",
                "allow_on_submit": 1,
                "no_copy": 1,
                "translatable": 1,
                "fetch_if_empty": 1,
            },
            {
                "fieldname": "vfd_rctnum",
                "label": "VFD RCTNUM",
                "fieldtype": "Data",
                "insert_after": "vfd_gc",
                "allow_on_submit": 1,
                "no_copy": 1,
                "read_only": 1,
                "translatable": 1,
            },
            {
                "fieldname": "vfd_rctvnum",
                "label": "VFD RCTVNUM",
                "fieldtype": "Data",
                "insert_after": "vfd_rctnum",
                "allow_on_submit": 1,
                "no_copy": 1,
                "read_only": 1,
                "translatable": 1,
            },
            {
                "fieldname": "vfd_serial",
                "label": "VFD SERIAL",
                "fieldtype": "Data",
                "insert_after": "vfd_rctvnum",
                "allow_on_submit": 1,
                "no_copy": 1,
                "read_only": 1,
                "translatable": 1,
            },
        ],
        "Item Tax Template": [
            {
                "fieldname": "vfd_taxcode",
                "label": "VFD TAXCODE",
                "fieldtype": "Select",
                "insert_after": "taxes",
                "no_copy": 1,
                "translatable": 1,
                "options": "\n1- Standard Rate (18%)\n2- Special Rate\n3- Zero rated\n4- Special Relief\n5- Exempt",
            },
        ],
        "Mode of Payment": [
            {
                "fieldname": "vfd_pmttype",
                "label": "VFD PMTTYPE",
                "fieldtype": "Select",
                "insert_after": "accounts",
                "no_copy": 1,
                "translatable": 1,
                "options": "\nCASH\nCHEQUE\nCCARD\nEMONEY\nINVOICE",
            },
        ],
    }

    create_custom_fields(fields, update=True)
