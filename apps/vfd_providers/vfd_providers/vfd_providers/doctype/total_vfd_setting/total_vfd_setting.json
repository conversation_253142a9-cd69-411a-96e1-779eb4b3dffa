{"actions": [], "allow_rename": 1, "autoname": "field:company", "creation": "2024-03-20 18:13:50.657076", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["company", "serial_id", "company_name", "alias_name", "tin", "vrn", "bearer_token", "x_active_business", "start_date", "section_break_rklsa", "is_vat_grouped"], "fields": [{"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "options": "Company", "reqd": 1, "set_only_once": 1, "unique": 1}, {"fieldname": "serial_id", "fieldtype": "Data", "label": "Serial ID"}, {"fieldname": "company_name", "fieldtype": "Data", "label": "Company Name", "read_only": 1}, {"fieldname": "alias_name", "fieldtype": "Data", "label": "<PERSON>as Name", "read_only": 1}, {"fieldname": "tin", "fieldtype": "Data", "label": "TIN"}, {"fieldname": "vrn", "fieldtype": "Data", "label": "VRN"}, {"fieldname": "bearer_token", "fieldtype": "Password", "label": "<PERSON><PERSON>", "length": 250}, {"fieldname": "x_active_business", "fieldtype": "Password", "label": "X Active Business"}, {"depends_on": "eval: doc.gov_reg_edate", "fieldname": "start_date", "fieldtype": "Date", "label": "Start Date"}, {"fieldname": "section_break_rklsa", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "is_vat_grouped", "fieldtype": "Check", "label": "Is VAT Grouped"}], "links": [], "modified": "2024-04-02 12:34:47.560984", "modified_by": "Administrator", "module": "VFD Providers", "name": "Total VFD Setting", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"delete": 1, "email": 1, "export": 1, "permlevel": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}