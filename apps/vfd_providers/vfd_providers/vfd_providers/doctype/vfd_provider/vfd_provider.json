{"actions": [], "allow_rename": 1, "autoname": "field:vfd_provider", "creation": "2023-01-29 09:56:52.142187", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["vfd_provider", "base_url", "column_break_iegqb", "vfd_provider_settings", "vfd_provider_controller", "section_break_idnrc", "attributes"], "fields": [{"fieldname": "vfd_provider", "fieldtype": "Data", "in_list_view": 1, "label": "VFD Provider", "reqd": 1, "unique": 1}, {"fieldname": "vfd_provider_settings", "fieldtype": "Link", "in_list_view": 1, "label": "VFD Provider Settings", "options": "DocType", "reqd": 1}, {"fieldname": "vfd_provider_controller", "fieldtype": "Data", "in_list_view": 1, "label": "VFD Provider Controller"}, {"fieldname": "attributes", "fieldtype": "Table", "label": "Attributes", "options": "VFD Provider Attribute"}, {"fieldname": "base_url", "fieldtype": "Data", "label": "Base URL", "options": "URL"}, {"fieldname": "column_break_iegqb", "fieldtype": "Column Break"}, {"fieldname": "section_break_idnrc", "fieldtype": "Section Break"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-01-29 11:24:25.376985", "modified_by": "Administrator", "module": "VFD Providers", "name": "VFD Provider", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}