{"actions": [], "allow_copy": 1, "autoname": "VFDPP-.YY.-.######", "creation": "2023-01-29 23:18:00.089985", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["sales_invoice", "ackcode", "ackmsg", "column_break_4", "rctnum", "date", "time", "section_break_10", "req_headers", "req_data"], "fields": [{"fieldname": "sales_invoice", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Sales Invoice", "options": "Sales Invoice", "read_only": 1}, {"fieldname": "ackcode", "fieldtype": "Int", "in_list_view": 1, "in_standard_filter": 1, "label": "ACKCODE", "read_only": 1}, {"fieldname": "ackmsg", "fieldtype": "Small Text", "in_list_view": 1, "in_standard_filter": 1, "label": "ACKMSG", "read_only": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "rctnum", "fieldtype": "Int", "in_list_view": 1, "in_standard_filter": 1, "label": "RCTNUM", "read_only": 1}, {"fieldname": "date", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "DATE", "read_only": 1}, {"fieldname": "time", "fieldtype": "Time", "label": "TIME", "read_only": 1}, {"fieldname": "section_break_10", "fieldtype": "Section Break"}, {"fieldname": "req_headers", "fieldtype": "Small Text", "label": "R<PERSON>Q Headers", "read_only": 1}, {"fieldname": "req_data", "fieldtype": "Code", "label": "REQ Data", "read_only": 1}], "links": [], "modified": "2023-01-29 23:18:00.089985", "modified_by": "Administrator", "module": "VFD Providers", "name": "VFD Provider Posting", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}