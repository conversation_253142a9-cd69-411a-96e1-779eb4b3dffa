{"absolute_value": 0, "align_labels_right": 0, "creation": "2025-04-23 15:13:08.031429", "css": ".print-format td, .print-format th {\n    padding: 2px !important;\n    font-size: 9pt;\n    padding: 5px !important;\n}\n\n.print-format {\n        margin-top: 0mm;\n        margin-left: 0mm;\n        margin-right: 0mm;\n        margin-bottom: 0mm;\n        font-size: 9pt !important;\t\n        border: 0px solid black;\n        padding: 0.25in;\n}\n\n.print-format hr {\n    border-top: 1px solid #333;\n    margin-top: 5px !important;\n    margin-bottom: 5px !important;\n}\n\n.print-format .letter-head {\n    margin-bottom: 10px;\n}\n.print-format .row:not(.section-break) {\n    line-height: 1.6;\n    margin-top: 0px !important;\n}\n\n.table {\n    margin-bottom: 5px !important;\n}\n\ntd.tight {\n  padding: 0px !important;\n}\n\n.ql-editor {\n    line-height: unset !important;\n}", "custom_format": 0, "default_print_language": "en", "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 2, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<table style=\\\"width: 100%;\\\">\\n    <tr>\\n        <td style=\\\"width: 50%; vertical-align: bottom;\\\">\\n            <p class=\\\"h2 text-center\\\"><b>\\n                {% if doc.select_print_heading is not none %}\\n                    {{ doc.select_print_heading }}\\n                {% else %}\\n                    TAX INVOICE\\n                {% endif %}\\n            </b></p>\\n        </td>\\n    </tr>\\n    <tr>\\n        <td style=\\\"width: 25%; vertical-align: bottom;\\\">\\n            <p class=\\\"h5 text-right\\\">\\n                TIN {{ frappe.db.get_value(\\\"Company\\\", doc.company, \\\"tin\\\") }}\\n            </p>\\n        </td>\\n        <td style=\\\"width: 25%; vertical-align: bottom;\\\">\\n            <p class=\\\"h5 text-right\\\">\\n                VRN {{ frappe.db.get_value(\\\"Company\\\", doc.company, \\\"vrn\\\") }}\\n            </p>\\n        </td>\\n    </tr>\\n</table>\\n<hr>\\n\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<table class=\\\"table table-borderless\\\" style=\\\"margin-top: 0 !important;\\\">\\r\\n  <tbody>\\r\\n    <tr>\\r\\n        <td class=\\\"col-xs-5 align-left align-top\\\" style=\\\"border-top: 0px;\\\">\\r\\n            <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                <div class=\\\"col-xs-3\\\">\\r\\n                    <label>Customer:</label>\\r\\n                </div>\\r\\n                <div class=\\\"col-xs-9 value\\\">\\r\\n                    <b>{{ doc.customer_name}}</b>\\r\\n                </div>\\r\\n            </div>\\r\\n            <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                <div class=\\\"col-xs-3\\\">\\r\\n                    <label>Address:</label>\\r\\n                </div>\\r\\n                <div class=\\\"col-xs-9 value\\\">\\r\\n                    {{ doc.address_display}}\\r\\n                </div>\\r\\n            </div>\\r\\n            {% if frappe.db.get_value(\\\"Customer\\\", doc.customer, \\\"vrn\\\") %}\\r\\n                <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                    <div class=\\\"col-xs-3\\\">\\r\\n                        <label>VRN:</label>\\r\\n                    </div>\\r\\n                    <div class=\\\"col-xs-9 value\\\">\\r\\n                        {{ frappe.db.get_value(\\\"Customer\\\", doc.customer, \\\"vrn\\\") }}\\r\\n                    </div>\\r\\n                </div>\\r\\n            {% endif %}\\r\\n            <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                <div class=\\\"col-xs-3\\\">\\r\\n                    <label>{{ doc.vfd_cust_id_type }}:</label>\\r\\n                </div>\\r\\n                <div class=\\\"col-xs-9 value\\\">\\r\\n                    {{ doc.vfd_cust_id }}\\r\\n                </div>\\r\\n            </div>\\r\\n        </td>\\r\\n        <td class=\\\"col-xs-5 align-left align-top\\\" style=\\\"border-top: 0px;\\\">\\r\\n            <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                <div class=\\\"col-xs-4\\\">\\r\\n                    {% if (doc.is_return == 0) %}\\r\\n                        <label>Tax Inv. No.:</label>\\r\\n                    {% else %}\\r\\n                        <label>Credit Note No.:</label>\\r\\n                    {% endif %}\\r\\n                </div>\\r\\n                <div class=\\\"col-xs-8 value\\\">\\r\\n                    <b>{{ doc.name}}</b>\\r\\n                </div>\\r\\n            </div>\\r\\n            <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                <div class=\\\"col-xs-4\\\">\\r\\n                    <label>Date:</label>\\r\\n                </div>\\r\\n                <div class=\\\"col-xs-8 value\\\">\\r\\n                    <b>{{ frappe.utils.formatdate(doc.vfd_date, 'dd-MMM-YYYY') }}</b>\\r\\n                </div>\\r\\n            </div>\\r\\n            {% if (doc.is_return == 0) %}    \\r\\n                <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                    <div class=\\\"col-xs-4\\\">\\r\\n                        <label>Order No.:</label>\\r\\n                    </div>\\r\\n                    <div class=\\\"col-xs-8 value\\\">\\r\\n                        {{ doc.po_no}}\\r\\n                    </div>\\r\\n                </div>\\r\\n                <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                    <div class=\\\"col-xs-4\\\">\\r\\n                        <label>Order Date:</label>\\r\\n                    </div>\\r\\n                    <div class=\\\"col-xs-8 value\\\">\\r\\n                        {{ frappe.utils.formatdate(doc.po_date, 'dd-MMM-YYYY') }}\\r\\n                    </div>\\r\\n                </div>\\r\\n                <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                    <div class=\\\"col-xs-4\\\">\\r\\n                        <label>Due Date:</label>\\r\\n                    </div>\\r\\n                    <div class=\\\"col-xs-8 value\\\">\\r\\n                        <b>{{ frappe.utils.formatdate(doc.due_date, 'dd-MMM-YYYY') }}</b>\\r\\n                    </div>\\r\\n                </div>\\r\\n                    {% if doc.vfd_serial %}\\r\\n                    <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                        <div class=\\\"col-xs-4\\\">\\r\\n                            <label>EFD Serial:</label>\\r\\n                        </div>\\r\\n                        <div class=\\\"col-xs-8 value\\\">\\r\\n                            <b>{{ doc.vfd_serial }}</b>\\r\\n                        </div>\\r\\n                    </div>\\r\\n                    {% endif %}\\r\\n                {% else %}\\r\\n                <div class=\\\"row  data-field\\\" data-fieldname=\\\"area\\\" data-fieldtype=\\\"Link\\\">\\r\\n                    <div class=\\\"col-xs-4\\\">\\r\\n                        <label>Against Tax Invoice:</label>\\r\\n                    </div>\\r\\n                    <div class=\\\"col-xs-8 value\\\">\\r\\n                        {{ doc.return_against}}\\r\\n                    </div>\\r\\n                </div>\\r\\n            {% endif %}\\r\\n        </td>\\r\\n        <td class=\\\"col-xs-2 text-center align-top\\\" style=\\\"border-top: 0px;\\\">\\r\\n            Receipt Verification Number<br>\\r\\n            {% if doc.vfd_rctvnum %}\\r\\n                <b>{{ doc.vfd_rctvnum }}</b><br><br>\\r\\n                {% set qr1 = generate_qrcode(doc.vfd_verification_url) %}\\r\\n                <img src=\\\"{{qr1}}\\\" style=\\\"max-width: 90px; max-height: 90px;\\\">\\r\\n            {% endif %}\\r\\n            <b>{{ doc.get_formatted('vfd_time') }}</b>\\r\\n\\r\\n        </td>\\r\\n    </tr>\\r\\n  </tbody>\\r\\n</table>\\r\\n\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<TABLE border=1 style=\\\"width: 100%; border-collapse: collapse; border: 1px solid black;\\\">\\r\\n\\t<colgroup>\\r\\n\\t\\t<col style=\\\"width: 5%\\\">\\r\\n\\t\\t<col style=\\\"width: 45%\\\">\\r\\n\\t\\t<col style=\\\"width: 10%\\\">\\r\\n\\t\\t<col style=\\\"width: 5%\\\">\\r\\n\\t\\t<col style=\\\"width: 15%\\\">\\r\\n\\t\\t<col style=\\\"width: 15%\\\">\\r\\n\\t</colgroup>\\r\\n\\t<THEAD>\\r\\n\\t\\t<TR>\\r\\n\\t\\t\\t<TH style=\\\"text-align:center\\\">SI</TH>\\r\\n\\t\\t\\t<TH style=\\\"text-align:center\\\">Particulars</TH>\\r\\n\\t\\t\\t<TH style=\\\"text-align:center\\\">QTY</TH>\\r\\n\\t\\t\\t<TH style=\\\"text-align:center\\\">VAT</TH>\\r\\n\\t\\t\\t<TH style=\\\"text-align:center\\\">Rate</TH>\\r\\n\\t\\t\\t<TH style=\\\"text-align:center\\\">Amount</TH>\\r\\n\\t\\t</TR>\\r\\n\\t</THEAD>\\r\\n\\t{% for item in doc.items %}\\r\\n\\t<TR>\\r\\n\\t\\t<TD>{{ loop.index }}</TD>\\r\\n\\t\\t<TD>\\r\\n \\t\\t\\t<b>{{ item.item_name }}</b>\\r\\n\\t\\t\\t{% if (item.description != item.item_name) %}\\r\\n\\t\\t\\t\\t<br>{{ item.description }}\\r\\n\\t\\t\\t{% endif %}\\r\\n\\t\\t</TD>\\r\\n\\t\\t<TD align=center>\\r\\n\\t\\t    {% if item.qty|int != 0 %} \\r\\n\\t\\t        {{ item.get_formatted(\\\"qty\\\", 0).replace(\\\"-\\\",\\\"\\\") }}\\r\\n            {% else %}\\t\\t        \\r\\n\\t\\t        {{ item.get_formatted(\\\"qty\\\", 2).replace(\\\"-\\\",\\\"\\\") }}\\r\\n\\t\\t    {% endif %}\\r\\n\\t\\t\\t<small>{{ item.get_formatted(\\\"uom\\\", 0) }}</small>\\r\\n\\t\\t</TD>\\r\\n\\t\\t<TD align=center>\\r\\n\\t\\t\\t{% set mydict = json.loads(item.item_tax_rate) %}\\r\\n\\t\\t\\t{% if item.item_tax_rate == \\\"{}\\\" %}\\r\\n\\t\\t\\t\\t{% for row in doc.taxes %}\\r\\n\\t\\t\\t\\t\\t{{ row.get_formatted(\\\"rate\\\",0) }}%\\r\\n\\t\\t\\t\\t{% endfor %}\\r\\n\\t\\t\\t{% else %}\\r\\n\\t\\t\\t\\t{% set ns = namespace(vat=none) %}\\r\\n\\t\\t\\t\\t{% for key, value in mydict.items() %}\\t\\r\\n\\t\\t\\t\\t\\t{% set ns.vat = value %}\\r\\n\\t\\t\\t\\t\\t{% endfor %}\\r\\n\\t\\t\\t\\t{{ ns.vat }}%\\r\\n\\t\\t\\t{% endif %}\\r\\n\\t\\t</TD>\\r\\n\\t\\t<TD align=right>{{ item.get_formatted(\\\"net_rate\\\", doc) }}</TD>\\r\\n\\t\\t<TD align=right>{{ frappe.utils.fmt_money('%0.2f'|format(item.net_amount|float|abs), currency=doc.currency) }}</TD>\\r\\n\\t</TR>\\r\\n \\t{% endfor %}\\r\\n\\t<TR>\\r\\n\\t\\t<TD colspan=6 align=right>\\r\\n            <i><small><strong>E&OE</strong></small></i>\\r\\n        </TD>\\r\\n\\t</TR>\\r\\n \\t\\r\\n\\t<TR>\\r\\n        <TD rowspan=9 colspan=3 style=\\\"vertical-align: top !important;\\\">\\r\\n            {% if doc.conversion_rate != 1 %}\\r\\n                <b><u>EXCHANGE RATE</u></b><br>\\r\\n                <b>1 {{doc.currency}} = {{ frappe.utils.fmt_money('%0.0f'|format(doc.conversion_rate|float|abs), currency=\\\"TZS\\\") }}</b><br>\\r\\n            {% endif %}<br>\\r\\n            <b><u>IN WORDS</u></b><br>\\r\\n            <B>{{ doc.in_words }}</B><br><br>\\r\\n            <B>\\r\\n               {% if doc.currency != \\\"TZS\\\" %}\\r\\n               {{ doc.base_in_words }}\\r\\n               {% endif %} \\r\\n            </B>\\r\\n            <hr><br>\\r\\n            {{ frappe.db.get_value(\\\"Company\\\", doc.company, \\\"company_bank_details\\\") }}\\t\\t\\t    \\r\\n        </TD>\\r\\n        <TD align=right colspan=2><B>SUB. TOTAL</B></TD>\\r\\n\\t\\t<TD align=right><B>{{ frappe.utils.fmt_money('%0.2f'|format(doc.net_total|float|abs), currency=doc.currency) }}</B></TD>\\r\\n\\t</TR>\\r\\n\\t<TR>\\r\\n\\t\\t<TD align=right colspan=2><B>V.A.T.</B></TD>\\r\\n\\t\\t<TD align=right><B>{{ frappe.utils.fmt_money('%0.2f'|format(doc.total_taxes_and_charges|float|abs), currency=doc.currency) }}</B></TD>\\r\\n\\t\\r\\n\\t</TR>\\r\\n\\t<TR>\\r\\n\\t\\t<TD align=right colspan=2><B>TOTAL</B></TD>\\r\\n\\t\\t<TD align=right><B>{{ frappe.utils.fmt_money('%0.2f'|format(doc.grand_total|float|abs), currency=doc.currency) }}</B></TD>\\r\\n\\t</TR>\\r\\n\\t<TR>\\r\\n        <TD align=left colspan=2><STRONG>Assessable Value:</STRONG></TD>\\r\\n\\t\\t<TD align=right>\\r\\n\\t\\t\\t{% if (doc.base_total_taxes_and_charges == 0) %}\\r\\n\\t\\t\\t\\t{{ frappe.utils.fmt_money(0, currency=\\\"\\\") }}\\r\\n\\t\\t\\t{% else %}\\r\\n\\t\\t\\t\\t{% if (doc.base_net_total != doc.base_total) %}\\r\\n\\t\\t\\t\\t\\t{{ frappe.utils.fmt_money(doc.base_net_total, currency=\\\"\\\") }}\\r\\n\\t\\t\\t\\t{% else %}\\r\\n\\t\\t\\t\\t\\t{{ frappe.utils.fmt_money(doc.base_net_total, currency=\\\"\\\") }}\\r\\n\\t\\t\\t\\t{% endif %}\\r\\n\\t\\t\\t{% endif %}\\r\\n\\t\\t</TD>\\r\\n\\t</TR>\\r\\n\\t<TR>\\r\\n            <TD align=left colspan=2><STRONG>VAT Amount:</STRONG></TD>\\r\\n            <TD align=right>\\r\\n                {{ frappe.utils.fmt_money('%0.2f'|format(doc.base_total_taxes_and_charges), currency=\\\"\\\") }}\\r\\n            </TD>\\r\\n\\t</TR>\\r\\n\\t<TR>\\r\\n\\t\\t{% if (doc.is_return == 0) %}\\r\\n\\t\\t\\t<TD align=left colspan=2><STRONG>Exempt:</STRONG></TD>\\r\\n\\t\\t\\t<TD align=right>\\r\\n\\t\\t\\t\\t{% if (doc.base_total_taxes_and_charges == 0) %}\\r\\n\\t\\t\\t\\t\\t{{ frappe.utils.fmt_money(doc.base_total, currency=\\\"\\\") }}\\r\\n\\t\\t\\t\\t{% else %}\\r\\n\\t\\t\\t\\t\\t{% if (doc.base_net_total != doc.base_total) %}\\r\\n\\t\\t\\t\\t\\t\\t{{ frappe.utils.fmt_money(doc.base_grand_total - (doc.base_net_total + doc.base_total_taxes_and_charges), currency=\\\"\\\") }}\\r\\n\\t\\t\\t\\t\\t{% else %}\\r\\n\\t\\t\\t\\t\\t\\t{{ frappe.utils.fmt_money(doc.base_grand_total - (doc.base_net_total + doc.base_total_taxes_and_charges), currency=\\\"\\\") }}\\r\\n\\t\\t\\t\\t\\t{% endif %}\\r\\n\\t\\t\\t\\t{% endif %}\\r\\n\\t\\t\\t</TD>\\r\\n\\t\\t{% endif %}\\r\\n\\t</TR>\\r\\n\\t<TR>\\r\\n\\t\\t{% if (doc.is_return == 0) %}\\r\\n\\t\\t\\t<TD align=left colspan=2><STRONG>Special Relief:</STRONG></TD>\\r\\n\\t\\t\\t<TD align=right>&nbsp;</TD>\\r\\n\\t\\t{% endif %}\\r\\n\\t</TR>\\r\\n\\t<TR>\\r\\n\\t\\t{% if (doc.is_return == 0) %}\\r\\n\\t\\t\\t<TD align=left colspan=2><STRONG>Zero Rated:</STRONG></TD>\\r\\n\\t\\t\\t<TD align=right>&nbsp;</TD>\\r\\n\\t\\t{% endif %}\\r\\n\\t</TR>\\r\\n\\t<TR>\\r\\n\\t\\t{% if (doc.is_return == 0) %}\\r\\n\\t\\t\\t<TD align=left colspan=2><STRONG>Total:</STRONG></TD>\\r\\n\\t\\t\\t<TD align=right style=\\\"border-top: 2px solid black;\\\">\\r\\n\\t\\t\\t\\t<b>{{ frappe.utils.fmt_money('%0.2f'| format(doc.base_grand_total), currency=\\\"\\\") }}</b>\\r\\n\\t\\t\\t</TD>\\r\\n\\t\\t{% endif %}\\r\\n\\t</TR>\\r\\n</TABLE>\\r\\n\\r\\n\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<div style=\\\"width: 60%; text-align: center; margin-top: 10px; margin-bottom: 0px; margin-left: auto;  margin-right: auto; font-weight: bold;  font-style: italic;\\\">\\n    This is computer generated invoice no signature is required\\n</div>\"}]", "html": "", "idx": 0, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2025-04-23 15:13:41.219077", "modified_by": "Administrator", "module": "VFD Providers", "name": "AV TI VFD", "owner": "Administrator", "page_number": "<PERSON>de", "print_format_builder": 1, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}