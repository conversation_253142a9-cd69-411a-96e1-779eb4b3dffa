{"absolute_value": 0, "align_labels_right": 0, "creation": "2025-04-23 15:13:35.644939", "css": "/*<td class=\"text-right\">{{ item.qty }}<br>@ {{ item.get_formatted(\"rate\") }}</td>*/", "custom_format": 1, "default_print_language": "en", "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font": "<PERSON><PERSON><PERSON>", "font_size": 14, "html": "<style>\n    .print-format{\n        font-size: 8pt;\n    }\n\t.print-format table, .print-format tr, \n\t.print-format td, .print-format div, .print-format p {\n\t\tline-height: 150%;\n\t\tvertical-align: middle;\n\t}\n\t.print-format table{\n\t    margin: 0px !important;\n\t}\n\t.print-format td, .print-format th {\n\t    padding: 0px !important;\n\t    border: 0px !important;\n\t}\n\n}\n\n\n\t@media screen {\n\t\t.print-format {\n\t\t    font-size: 7pt;\n\t\t\twidth: 4in;\n\t\t\tpadding: 0.15in;\n\t\t\tmin-height: 8in;\n\t\t}\n\t}\n</style>\n<p class=\"text-center\" style=\"margin-bottom: 1rem\">\n\t<strong style=\"font-size: 12pt; text-transform: uppercase;\">{{ doc.company }}</strong><br>\n\tP. O. Box: {{ frappe.db.get_value(\"Company\", doc.company, \"p_o_box\") }}, \n\t{{ frappe.db.get_value(\"Company\", doc.company, \"city\") }}<br>\n\tTel: , {{ frappe.db.get_value(\"Company\", doc.company, \"phone_no\") }}<br>\n\tStreet: , {{ frappe.db.get_value(\"Company\", doc.company, \"street\") }}<br><br>\n\t<strong style=\"font-size: 12pt; text-transform: uppercase; margin-top: 5px;\">{{ doc.select_print_heading or _(\"TAX INVOICE\") }}</strong>\n</p>\n<p>\n\t<b>{{ _(\"Receipt No\") }}:</b> {{ doc.name }}<br>\n\t<b>{{ _(\"Customer\") }}:</b> {{ doc.customer_name }}<br>\n\t<b>{{ _(\"Date\") }}:</b> {{ frappe.utils.formatdate(doc.posting_date, 'dd-MMM-YYYY') }} {{  doc.get_formatted(\"posting_time\") }}<br>\n\t<b>{{ _(\"TIN\") }}</b>   {{ frappe.db.get_value(\"Customer\", doc.customer, \"tax_id\") }}<br>\n\t<b>{{ _(\"VRN\") }}</b>   {{ frappe.db.get_value(\"Customer\", doc.customer, \"vrn\") }}\n</p>\n<table class=\"table table-condensed\">\n\t<tr>\n\t\t<td width=\"40%\">{{ _(\"Item\") }}</td>\n\t\t<td width=\"5%\" class=\"text-right\">{{ _(\"Qty\") }}</td>\n\t\t<td width=\"25%\" class=\"text-right\">{{ _(\"Rate\") }}</td>\n\t\t<td width=\"30%\" class=\"text-right\">{{ _(\"Amount\") }}</td>\n\t</tr>\n\t<tbody>\n\t\t{%- for item in doc.items -%}\n\t\t<tr>\n\t\t\t<td>\n\t\t\t\t{{ item.item_code }}\n\t\t\t\t{%- if item.item_name != item.item_code -%}\n\t\t\t\t\t<br>{{ item.item_name }}\n\t\t\t\t{%- endif -%}\n\t\t\t\t{%- if item.serial_no -%}\n\t\t\t\t\t<br><b>{{ _(\"SR.No\") }}:</b><br>\n\t\t\t\t\t{{ item.serial_no | replace(\"\\n\", \", \") }}\n\t\t\t\t{%- endif -%}\n\t\t\t</td>\n\t\t\t<td class=\"text-right\">{{ item.qty }} </td>\n\t\t\t<td class=\"text-right\">{{ frappe.utils.fmt_money('%0.2f'|format(item.rate)) }}</td>\n\t\t\t<td class=\"text-right\">{{ frappe.utils.fmt_money('%0.2f'|format(item.amount)) }}</td>\n\t\t</tr>\n\t\t{%- endfor -%}\n\t</tbody>\n</table><br>\n<table class=\"table table-condensed\">\n\t<tbody>\n\t\t<tr>\n\t\t\t<td class=\"text-left\" style=\"padding: 0px; !important\" colspan=3>\n\t\t\t\t<b>{{ _(\"Total\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\" style=\"padding: 0px; !important\" >\n\t\t\t\t{{ frappe.utils.fmt_money('%0.2f'|format(doc.net_total)) }}\n\t\t\t</td>\n\t\t</tr>\n\t\t{%- for row in doc.taxes -%}\n\t\t\t<tr>\n\t\t\t\t<td class=\"text-left\" colspan=3 style=\"padding: 0px; !important\" >\n\t\t\t\t    {% if '%' in row.description %}\n\t\t\t\t\t    <b>{{ _(\"VAT@18%\") }}</b>\n\t\t\t\t\t{% else %}\n\t\t\t\t\t    <b>{{ _(\"VAT@18%\") }}</b>\n\t\t\t\t\t{% endif %}\n\t\t\t\t</td>\n\t\t\t\t<td class=\"text-right\" style=\"padding: 0px; !important\" >\n\t\t\t\t\t{{ frappe.utils.fmt_money('%0.2f'|format(row.tax_amount)) }}\n\t\t\t\t</td>\n\t\t\t<tr>\n\t\t{%- endfor -%}\n\n\t\t<tr>\n\t\t\t<td class=\"text-left\" colspan=3 style=\"padding: 0px;\" >\n\t\t\t\t<b>{{ _(\"Grand Total\") }}</b>\n\t\t\t</td>\n\t\t\t<td class=\"text-right\" style=\"padding: 0px;\" >\n\t\t\t\t{{ frappe.utils.fmt_money('%0.2f'|format(doc.grand_total)) }}\n\t\t\t</td>\n\t\t</tr>\n\t</tbody>\n</table>\n<p  class=\"text-center\">\n    <b>Receipt Verification Number</b><br>\n    {% if doc.vfd_rctvnum %}\n        <b>{{ doc.vfd_rctvnum }}</b><br>\n        {% set qr1 = generate_qrcode(doc.vfd_verification_url) %}\n        <img src=\"{{qr1}}\" style=\"max-width: 90px; max-height: 90px;\"><br>\n        {{  doc.get_formatted(\"vfd_time\") }}\n    {% endif %}\n</p>\n\n<p>{{ doc.terms or \"\" }}</p>\n<p class=\"text-center\"><b>{{ _(\"Thank you, please visit again.\") }}</b>.<br>.<br></p>", "idx": 0, "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2025-04-23 15:13:35.644939", "modified_by": "Administrator", "module": "VFD Providers", "name": "SI POS Inv", "owner": "Administrator", "page_number": "<PERSON>de", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}