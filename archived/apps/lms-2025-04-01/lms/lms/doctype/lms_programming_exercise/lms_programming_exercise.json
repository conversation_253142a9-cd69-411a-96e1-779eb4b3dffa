{"actions": [], "allow_rename": 1, "creation": "2025-06-18 15:02:36.198855", "doctype": "DocType", "engine": "InnoDB", "field_order": ["title", "column_break_jlzi", "language", "section_break_tjwv", "problem_statement", "section_break_ftkh", "test_cases"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"fieldname": "problem_statement", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Problem Statement", "reqd": 1}, {"default": "Python", "fieldname": "language", "fieldtype": "Select", "label": "Language", "options": "Python\nJavaScript", "reqd": 1}, {"fieldname": "column_break_jlzi", "fieldtype": "Column Break"}, {"fieldname": "section_break_tjwv", "fieldtype": "Section Break"}, {"fieldname": "section_break_ftkh", "fieldtype": "Section Break"}, {"fieldname": "test_cases", "fieldtype": "Table", "label": "Test Cases", "options": "LMS Test Case"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [{"link_doctype": "LMS Programming Exercise Submission", "link_fieldname": "exercise"}], "modified": "2025-06-24 14:42:27.463492", "modified_by": "<EMAIL>", "module": "LMS", "name": "LMS Programming Exercise", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Moderator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Course Creator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Batch Evaluator", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "LMS Student", "share": 1}], "row_format": "Dynamic", "show_title_field_in_link": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "title"}