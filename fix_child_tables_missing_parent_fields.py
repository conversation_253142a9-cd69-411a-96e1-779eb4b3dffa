#!/usr/bin/env python3
"""
<PERSON>ript to fix child tables missing parent, parentfield, and parenttype fields in csf_tz app.
This script will add the missing fields to all child tables that don't have them.
"""

import os
import json
import frappe
from frappe.model.sync import sync_for


def get_child_tables_in_app(app_name="csf_tz"):
    """Get all child tables in the specified app"""
    app_path = frappe.get_app_path(app_name)
    child_tables = []
    
    # Walk through all doctype directories
    for root, dirs, files in os.walk(app_path):
        if 'doctype' in root and files:
            for file in files:
                if file.endswith('.json') and not file.startswith('.'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r') as f:
                            doctype_data = json.load(f)
                            
                        # Check if it's a child table
                        if doctype_data.get('istable') == 1:
                            child_tables.append({
                                'name': doctype_data.get('name'),
                                'path': file_path,
                                'data': doctype_data
                            })
                    except (json.<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>):
                        continue
    
    return child_tables


def check_missing_parent_fields(doctype_data):
    """Check which parent fields are missing from a child table"""
    required_fields = ['parent', 'parentfield', 'parenttype']
    existing_fields = [field.get('fieldname') for field in doctype_data.get('fields', [])]
    missing_fields = [field for field in required_fields if field not in existing_fields]
    return missing_fields


def add_parent_fields_to_doctype(doctype_data):
    """Add missing parent fields to a child table doctype"""
    missing_fields = check_missing_parent_fields(doctype_data)
    
    if not missing_fields:
        return False, "No missing fields"
    
    # Standard parent fields for child tables
    parent_fields = [
        {
            "fieldname": "parent",
            "fieldtype": "Data",
            "hidden": 1,
            "label": "Parent",
            "no_copy": 1,
            "print_hide": 1,
            "read_only": 1
        },
        {
            "fieldname": "parentfield",
            "fieldtype": "Data", 
            "hidden": 1,
            "label": "Parent Field",
            "no_copy": 1,
            "print_hide": 1,
            "read_only": 1
        },
        {
            "fieldname": "parenttype",
            "fieldtype": "Data",
            "hidden": 1,
            "label": "Parent Type",
            "no_copy": 1,
            "print_hide": 1,
            "read_only": 1
        }
    ]
    
    # Add missing fields to field_order and fields
    for field in parent_fields:
        if field['fieldname'] in missing_fields:
            # Add to field_order
            if 'field_order' not in doctype_data:
                doctype_data['field_order'] = []
            doctype_data['field_order'].append(field['fieldname'])
            
            # Add to fields
            if 'fields' not in doctype_data:
                doctype_data['fields'] = []
            doctype_data['fields'].append(field)
    
    return True, f"Added fields: {', '.join(missing_fields)}"


def fix_child_table(table_info):
    """Fix a single child table by adding missing parent fields"""
    doctype_data = table_info['data']
    name = table_info['name']
    path = table_info['path']
    
    print(f"\nProcessing: {name}")
    print(f"Path: {path}")
    
    # Check what's missing
    missing_fields = check_missing_parent_fields(doctype_data)
    if not missing_fields:
        print(f"✓ {name} already has all required parent fields")
        return True
    
    print(f"Missing fields: {', '.join(missing_fields)}")
    
    # Add missing fields
    modified, message = add_parent_fields_to_doctype(doctype_data)
    if not modified:
        print(f"✗ {name}: {message}")
        return False
    
    # Write back to file
    try:
        with open(path, 'w') as f:
            json.dump(doctype_data, f, indent=1, separators=(',', ': '))
        print(f"✓ {name}: {message}")
        return True
    except Exception as e:
        print(f"✗ {name}: Failed to write file - {str(e)}")
        return False


def main():
    """Main function to fix all child tables"""
    print("=== Fixing Child Tables Missing Parent Fields ===")
    
    # Get all child tables in csf_tz app
    child_tables = get_child_tables_in_app("csf_tz")
    print(f"\nFound {len(child_tables)} child tables in csf_tz app")
    
    # Process each child table
    fixed_count = 0
    error_count = 0
    
    for table in child_tables:
        try:
            if fix_child_table(table):
                fixed_count += 1
            else:
                error_count += 1
        except Exception as e:
            print(f"✗ {table['name']}: Unexpected error - {str(e)}")
            error_count += 1
    
    print(f"\n=== Summary ===")
    print(f"Total child tables: {len(child_tables)}")
    print(f"Successfully processed: {fixed_count}")
    print(f"Errors: {error_count}")
    
    if fixed_count > 0:
        print(f"\n⚠️  IMPORTANT: Run 'bench migrate' to apply database schema changes")


if __name__ == "__main__":
    main()
